using Amazon2FacebookPoster;

namespace Amazon2FacebookPoster.Tests
{
    /// <summary>
    /// Classe de test pour valider le fonctionnement de l'extraction des offres Amazon
    /// </summary>
    public class TestAmazonDeals
    {
        /// <summary>
        /// Test complet du processus d'extraction et de génération
        /// </summary>
        public static async Task RunCompleteTest()
        {
            Console.WriteLine("🧪 DÉMARRAGE DES TESTS AMAZON DEALS");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();

            var geminiApiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
            if (string.IsNullOrEmpty(geminiApiKey))
            {
                Console.WriteLine("❌ GEMINI_API_KEY non configurée");
                return;
            }

            try
            {
                // Test 1: Extraction basique
                await TestBasicExtraction(geminiApiKey);
                
                // Test 2: Génération de posts
                await TestPostGeneration(geminiApiKey);
                
                // Test 3: Traitement JSON
                await TestJsonProcessing(geminiApiKey);

                Console.WriteLine();
                Console.WriteLine("✅ TOUS LES TESTS RÉUSSIS !");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ÉCHEC DES TESTS : {ex.Message}");
            }
        }

        /// <summary>
        /// Test 1: Extraction basique des produits
        /// </summary>
        private static async Task TestBasicExtraction(string geminiApiKey)
        {
            Console.WriteLine("📋 Test 1: Extraction basique des produits");
            Console.WriteLine("-" + new string('-', 40));

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AmazonDealsExtractor(amazonLoader);
            
            try
            {
                await amazonLoader.StartBrowser(headless: true);
                Console.WriteLine("✅ Navigateur démarré");

                var products = await extractor.ExtractAllDealsProducts(maxPages: 1);
                Console.WriteLine($"✅ {products.Count} produits extraits");

                if (products.Count > 0)
                {
                    var firstProduct = products[0];
                    Console.WriteLine($"   Premier produit: {firstProduct.Title}");
                    Console.WriteLine($"   URL: {firstProduct.ProductUrl}");
                    Console.WriteLine($"   Prix: {firstProduct.Price}");
                }

                // Générer les liens d'affiliation
                extractor.GenerateAffiliateLinks("test-tag-20");
                Console.WriteLine("✅ Liens d'affiliation générés");

                await amazonLoader.CloseBrowser();
                Console.WriteLine("✅ Test 1 réussi");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test 1 échoué: {ex.Message}");
                await amazonLoader.CloseBrowser();
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Test 2: Génération de posts Facebook
        /// </summary>
        private static async Task TestPostGeneration(string geminiApiKey)
        {
            Console.WriteLine("📝 Test 2: Génération de posts Facebook");
            Console.WriteLine("-" + new string('-', 40));

            var postGenerator = new FacebookPostGenerator(geminiApiKey);
            
            try
            {
                // Test avec une URL de produit connue
                var testUrl = "https://www.amazon.fr/dp/B0F6VHH8KC";
                var testAffiliateLink = "https://amzn.to/test123";

                var post = await postGenerator.GeneratePostFromUrl(testUrl, testAffiliateLink);
                
                if (!string.IsNullOrEmpty(post) && !post.Contains("Erreur"))
                {
                    Console.WriteLine("✅ Post généré avec succès");
                    Console.WriteLine($"   Longueur: {post.Length} caractères");
                    
                    // Vérifier que le post contient les éléments essentiels
                    var hasPrice = post.Contains("€") || post.Contains("PRIX");
                    var hasLink = post.Contains(testAffiliateLink);
                    var hasEmojis = post.Contains("💥") || post.Contains("✅");
                    
                    Console.WriteLine($"   Contient prix: {(hasPrice ? "✅" : "❌")}");
                    Console.WriteLine($"   Contient lien: {(hasLink ? "✅" : "❌")}");
                    Console.WriteLine($"   Contient emojis: {(hasEmojis ? "✅" : "❌")}");
                    
                    if (hasPrice && hasLink && hasEmojis)
                    {
                        Console.WriteLine("✅ Test 2 réussi");
                    }
                    else
                    {
                        Console.WriteLine("⚠️ Test 2 partiellement réussi");
                    }
                }
                else
                {
                    Console.WriteLine("❌ Échec de la génération de post");
                    throw new Exception("Post vide ou contient une erreur");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test 2 échoué: {ex.Message}");
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Test 3: Traitement depuis JSON
        /// </summary>
        private static async Task TestJsonProcessing(string geminiApiKey)
        {
            Console.WriteLine("📂 Test 3: Traitement depuis JSON");
            Console.WriteLine("-" + new string('-', 40));

            try
            {
                // Créer un fichier JSON de test
                var testProducts = new List<ProductInfo>
                {
                    new ProductInfo
                    {
                        Title = "Produit de test 1",
                        ProductUrl = "https://www.amazon.fr/dp/TEST001",
                        AffiliateLink = "https://amzn.to/test001",
                        Price = "19,99 €",
                        OriginalPrice = "29,99 €",
                        Discount = "-33%",
                        Rating = "4,5 étoiles sur 5",
                        ReviewCount = "123 évaluations"
                    },
                    new ProductInfo
                    {
                        Title = "Produit de test 2",
                        ProductUrl = "https://www.amazon.fr/dp/TEST002",
                        AffiliateLink = "https://amzn.to/test002",
                        Price = "39,99 €",
                        OriginalPrice = "59,99 €",
                        Discount = "-33%",
                        Rating = "4,2 étoiles sur 5",
                        ReviewCount = "456 évaluations"
                    }
                };

                var testJsonPath = "test_products.json";
                var json = System.Text.Json.JsonSerializer.Serialize(testProducts, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(testJsonPath, json);
                Console.WriteLine("✅ Fichier JSON de test créé");

                // Tester le traitement depuis JSON
                var options = new ProcessingOptions
                {
                    OutputDirectory = "test_output",
                    GeneratePosts = false, // Pas de génération pour ce test
                    SaveProductsJson = false
                };

                var processor = new AmazonDealsProcessor(options);
                processor.SetGeminiApiKey(geminiApiKey);

                // Simuler le chargement JSON (sans génération de posts pour le test)
                if (File.Exists(testJsonPath))
                {
                    var loadedJson = await File.ReadAllTextAsync(testJsonPath);
                    var loadedProducts = System.Text.Json.JsonSerializer.Deserialize<List<ProductInfo>>(loadedJson);
                    
                    if (loadedProducts != null && loadedProducts.Count == 2)
                    {
                        Console.WriteLine($"✅ {loadedProducts.Count} produits chargés depuis JSON");
                        Console.WriteLine("✅ Test 3 réussi");
                    }
                    else
                    {
                        throw new Exception("Échec du chargement JSON");
                    }
                }

                // Nettoyer
                if (File.Exists(testJsonPath))
                {
                    File.Delete(testJsonPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test 3 échoué: {ex.Message}");
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Test de performance - mesure le temps d'extraction
        /// </summary>
        public static async Task RunPerformanceTest()
        {
            Console.WriteLine("⏱️ TEST DE PERFORMANCE");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine();

            var geminiApiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
            if (string.IsNullOrEmpty(geminiApiKey))
            {
                Console.WriteLine("❌ GEMINI_API_KEY non configurée");
                return;
            }

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AmazonDealsExtractor(amazonLoader);
            
            try
            {
                var startTime = DateTime.Now;
                
                await amazonLoader.StartBrowser(headless: true);
                var browserStartTime = DateTime.Now;
                
                var products = await extractor.ExtractAllDealsProducts(maxPages: 1);
                var extractionTime = DateTime.Now;
                
                extractor.GenerateAffiliateLinks("perf-test-20");
                var affiliateTime = DateTime.Now;
                
                await amazonLoader.CloseBrowser();
                var endTime = DateTime.Now;

                // Afficher les résultats
                Console.WriteLine("📊 RÉSULTATS DE PERFORMANCE:");
                Console.WriteLine($"   Démarrage navigateur: {(browserStartTime - startTime).TotalSeconds:F1}s");
                Console.WriteLine($"   Extraction produits: {(extractionTime - browserStartTime).TotalSeconds:F1}s");
                Console.WriteLine($"   Génération liens: {(affiliateTime - extractionTime).TotalSeconds:F1}s");
                Console.WriteLine($"   Fermeture navigateur: {(endTime - affiliateTime).TotalSeconds:F1}s");
                Console.WriteLine($"   TEMPS TOTAL: {(endTime - startTime).TotalSeconds:F1}s");
                Console.WriteLine($"   Produits extraits: {products.Count}");
                Console.WriteLine($"   Vitesse: {(products.Count / (extractionTime - browserStartTime).TotalSeconds):F1} produits/seconde");
                
                Console.WriteLine();
                Console.WriteLine("✅ Test de performance terminé");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test de performance échoué: {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Test de validation des données extraites
        /// </summary>
        public static async Task RunDataValidationTest()
        {
            Console.WriteLine("🔍 TEST DE VALIDATION DES DONNÉES");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();

            var geminiApiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
            if (string.IsNullOrEmpty(geminiApiKey))
            {
                Console.WriteLine("❌ GEMINI_API_KEY non configurée");
                return;
            }

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AmazonDealsExtractor(amazonLoader);
            
            try
            {
                await amazonLoader.StartBrowser(headless: true);
                var products = await extractor.ExtractAllDealsProducts(maxPages: 1);
                
                if (products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit extrait");
                    return;
                }

                int validProducts = 0;
                int productsWithTitle = 0;
                int productsWithUrl = 0;
                int productsWithPrice = 0;
                int productsWithDiscount = 0;

                foreach (var product in products)
                {
                    bool isValid = true;

                    if (!string.IsNullOrEmpty(product.Title))
                        productsWithTitle++;
                    else
                        isValid = false;

                    if (!string.IsNullOrEmpty(product.ProductUrl) && product.ProductUrl.Contains("/dp/"))
                        productsWithUrl++;
                    else
                        isValid = false;

                    if (!string.IsNullOrEmpty(product.Price))
                        productsWithPrice++;

                    if (!string.IsNullOrEmpty(product.Discount))
                        productsWithDiscount++;

                    if (isValid)
                        validProducts++;
                }

                Console.WriteLine("📊 RÉSULTATS DE VALIDATION:");
                Console.WriteLine($"   Total produits: {products.Count}");
                Console.WriteLine($"   Produits valides: {validProducts} ({(validProducts * 100.0 / products.Count):F1}%)");
                Console.WriteLine($"   Avec titre: {productsWithTitle} ({(productsWithTitle * 100.0 / products.Count):F1}%)");
                Console.WriteLine($"   Avec URL: {productsWithUrl} ({(productsWithUrl * 100.0 / products.Count):F1}%)");
                Console.WriteLine($"   Avec prix: {productsWithPrice} ({(productsWithPrice * 100.0 / products.Count):F1}%)");
                Console.WriteLine($"   Avec réduction: {productsWithDiscount} ({(productsWithDiscount * 100.0 / products.Count):F1}%)");

                if (validProducts > products.Count * 0.8) // 80% de produits valides
                {
                    Console.WriteLine("✅ Qualité des données: EXCELLENTE");
                }
                else if (validProducts > products.Count * 0.6) // 60% de produits valides
                {
                    Console.WriteLine("⚠️ Qualité des données: CORRECTE");
                }
                else
                {
                    Console.WriteLine("❌ Qualité des données: FAIBLE");
                }

                await amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test de validation échoué: {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }
    }
}
