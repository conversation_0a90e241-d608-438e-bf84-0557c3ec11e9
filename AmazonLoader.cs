﻿using PuppeteerSharp;

namespace Amazon2FacebookPoster
{
    public class AmazonLoader<T> where T : class
    {
        private IBrowser? browser;
        private IPage? page;
        private FacebookPostGenerator? _postGenerator;

        public AmazonLoader()
        {
        }

        public void SetGeminiApiKey(string apiKey)
        {
            _postGenerator = new FacebookPostGenerator(apiKey);
        }

        public async Task CloseBrowser()
        {
            if (browser != null)
            {
                await browser.CloseAsync();
            }
        }

        public async Task StartBrowser(bool headless = true, CancellationToken cancellationToken = default)
        {
            await new BrowserFetcher().DownloadAsync();

            // Define user data directory to persist login sessions
            var userDataDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Amazon2FacebookPoster", "ChromeUserData");
            Directory.CreateDirectory(userDataDir);

            var browserArgs = new[] {
                                //"--disable-gpu",  // Disable GPU hardware acceleration
                                //"--disable-dev-shm-usage",  // Prevent running out of memory
                                //"--js-flags=--max-old-space-size=512",  // Limit memory usage
                                //"--single-process",  // Run in single process mode for better performance
                                //"--no-sandbox",
                                //"--disable-setuid-sandbox",
                                //"--start-fullscreen",  // Enable fullscreen mode
                                //"--window-size=1920,1080",
                                //"--disable-features=site-per-process",
                                "--lang=fr-FR"  // Set French language
                            };
            //var launchArgs = JsonSerializer.Serialize(new
            //{
            //    args = browserArgs,
            //    headless = true,  // True headless mode for better performance
            //    waitUntil = "networkidle0"  // Wait for page to be fully loaded
            //});

            //browser = await Puppeteer.ConnectAsync(new ConnectOptions
            //{
            //    BrowserWSEndpoint = $"wss://browserless.atlaprest.com?token=iqV6qATinekY1HZYxToqvbpID7lfueYP&launch={launchArgs}",
            //    DefaultViewport = null,  // Required for fullscreen
            //    AcceptInsecureCerts = true,

            //});
            browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = headless,
                //Devtools = !headless,
                Args = browserArgs,
                UserDataDir = userDataDir,  // Use persistent user data directory to maintain login sessions
#if !DEBUG
                ExecutablePath = "/usr/bin/google-chrome",
#endif
                DefaultViewport = new ViewPortOptions
                {
                    Width = 1920,
                    Height = 1080
                },
                AcceptInsecureCerts = true
            });
            page = await browser.NewPageAsync();
        }

        private async Task TryAndExecute(string link)
        {
            if (browser == null || page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            try
            {
                if (page.IsClosed)
                    page = await browser.NewPageAsync();
                await page.GoToAsync(link, new NavigationOptions { WaitUntil = new WaitUntilNavigation[] { WaitUntilNavigation.Networkidle0 } });
            }
            catch (NavigationException)
            {
                await Task.Delay(1000);
                await TryAndExecute(link);
            }
        }

        public async Task<T> LoadPage(string link, Func<IPage, Task<T>> action, CancellationToken cancellationToken = default)
        {
            if (page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            await TryAndExecute(link);
            var result = await action(page);
            return result;
        }

        public async Task<string> LoadPageAndGenerateFacebookPost(string productUrl, string affiliateLink, CancellationToken cancellationToken = default)
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            if (page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            await TryAndExecute(productUrl);

            // Attendre que la page soit complètement chargée
            await Task.Delay(3000, cancellationToken);

            // NOUVEAU: Capturer un screenshot pour la page produit individuel
            var screenshotPath = await CaptureProductScreenshot(productUrl);

            // Générer le post Facebook basé sur le contenu de la page avec screenshot
            var facebookPost = await _postGenerator.GeneratePostFromPageContentWithScreenshot(page, affiliateLink, screenshotPath);

            Console.WriteLine("=== POST FACEBOOK GÉNÉRÉ ===");
            Console.WriteLine(facebookPost);
            Console.WriteLine("=== FIN DU POST ===");

            return facebookPost;
        }

        /// <summary>
        /// Capture un screenshot spécifiquement pour une page produit individuel
        /// </summary>
        private async Task<string> CaptureProductScreenshot(string productUrl)
        {
            try
            {
                if (page == null) return "";

                Console.WriteLine($"📸 Capture screenshot pour page produit individuel...");

                // Scroll pour charger le contenu complet
                await page.EvaluateFunctionAsync(@"
                    async () => {
                        const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

                        // Scroll progressif pour charger le contenu
                        const scrollStep = 800;
                        const maxScrolls = 5;

                        for (let i = 0; i < maxScrolls; i++) {
                            window.scrollTo(0, i * scrollStep);
                            await wait(500);
                        }

                        // Retour en haut pour la capture
                        window.scrollTo(0, 0);
                        await wait(1000);
                    }");

                // Créer le répertoire screenshots
                var screenshotsDir = Path.Combine(Directory.GetCurrentDirectory(), "screenshots", "products");
                Directory.CreateDirectory(screenshotsDir);

                // Extraire l'ASIN de l'URL pour le nom du fichier
                var asin = ExtractAsinFromUrl(productUrl);
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var filename = $"product_{asin ?? "unknown"}_{timestamp}.png";
                var screenshotPath = Path.Combine(screenshotsDir, filename);

                // Capturer le screenshot
                await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                {
                    FullPage = false, // Capture visible seulement pour les produits
                    Type = ScreenshotType.Png
                });

                // Vérifier que le fichier a été créé
                var fileInfo = new FileInfo(screenshotPath);
                if (fileInfo.Exists && fileInfo.Length > 1000)
                {
                    Console.WriteLine($"📸 Screenshot produit sauvegardé : {filename} ({fileInfo.Length / 1024:N0} KB)");
                    return screenshotPath;
                }
                else
                {
                    Console.WriteLine($"⚠️ Screenshot produit vide ou trop petit");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur capture screenshot produit : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Extrait l'ASIN d'une URL Amazon
        /// </summary>
        private string? ExtractAsinFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return null;
            var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            return asinMatch.Success ? asinMatch.Groups[1].Value : null;
        }

        public async Task<string> GenerateFacebookPostFromUrl(string productUrl, string affiliateLink)
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            var facebookPost = await _postGenerator.GeneratePostFromUrl(productUrl, affiliateLink);

            Console.WriteLine("=== POST FACEBOOK GÉNÉRÉ ===");
            Console.WriteLine(facebookPost);
            Console.WriteLine("=== FIN DU POST ===");

            return facebookPost;
        }
    }
}