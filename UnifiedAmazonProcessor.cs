namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Unified processor that uses strategy pattern for different extraction methods
    /// </summary>
    public class UnifiedAmazonProcessor : BaseAmazonProcessor
    {
        private readonly IExtractionStrategy _extractionStrategy;
        private readonly ProductDetailExtractor _detailExtractor;

        public UnifiedAmazonProcessor(AmazonScrapingConfiguration config) : base(config)
        {
            _extractionStrategy = CreateExtractionStrategy(config);
            _detailExtractor = new ProductDetailExtractor();
        }

        /// <summary>
        /// Factory method to create appropriate extraction strategy
        /// </summary>
        private IExtractionStrategy CreateExtractionStrategy(AmazonScrapingConfiguration config)
        {
            return config.ExtractionStrategy switch
            {
                ExtractionStrategy.Direct => new DirectExtractionStrategy(),
                ExtractionStrategy.AI => new AIExtractionStrategy(config.GeminiApiKey),
                ExtractionStrategy.Hybrid => new HybridExtractionStrategy(config.GeminiApiKey),
                _ => new HybridExtractionStrategy(config.<PERSON><PERSON><PERSON>ey)
            };
        }

        /// <summary>
        /// Extract products using the configured strategy
        /// </summary>
        protected override async Task<List<ProductInfo>> ExtractProducts()
        {
            Console.WriteLine($"🎯 Starting extraction with {_extractionStrategy.StrategyName}...");

            var products = await _amazonLoader.LoadPage("https://www.amazon.fr/deals", async page =>
            {
                return await _extractionStrategy.ExtractProductsAsync(page, _config);
            });

            // Enhance products with detailed information if requested
            if (products.Count > 0 && _config.ExtractionStrategy != ExtractionStrategy.Direct)
            {
                products = await EnhanceProductsWithDetails(products);
            }

            return products;
        }

        /// <summary>
        /// Enhance products with detailed information by visiting individual product pages
        /// </summary>
        private async Task<List<ProductInfo>> EnhanceProductsWithDetails(List<ProductInfo> products)
        {
            Console.WriteLine($"🔍 Enhancing {products.Count} products with detailed information...");
            
            var enhancedProducts = new List<ProductInfo>();
            var maxProductsToEnhance = Math.Min(products.Count, 20); // Limit to avoid long processing times

            for (int i = 0; i < maxProductsToEnhance; i++)
            {
                var product = products[i];
                
                try
                {
                    Console.WriteLine($"🔍 Enhancing product {i + 1}/{maxProductsToEnhance}: {product.GetSummary()}");

                    var enhancedProduct = await _amazonLoader.LoadPage(product.ProductUrl, async page =>
                    {
                        return await _detailExtractor.ExtractCompleteProductDetails(page, product, _config.AmazonAssociateTag);
                    });

                    enhancedProducts.Add(enhancedProduct);

                    // Add delay between product page visits
                    if (i < maxProductsToEnhance - 1)
                    {
                        await Task.Delay(Random.Shared.Next(2000, 5000));
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ Failed to enhance product: {ex.Message}");
                    // Add the original product without enhancement
                    enhancedProducts.Add(product);
                }
            }

            // Add remaining products without enhancement
            if (maxProductsToEnhance < products.Count)
            {
                enhancedProducts.AddRange(products.Skip(maxProductsToEnhance));
                Console.WriteLine($"   ℹ️ Added {products.Count - maxProductsToEnhance} products without detailed enhancement");
            }

            Console.WriteLine($"✅ Product enhancement completed: {enhancedProducts.Count} products ready");
            return enhancedProducts;
        }

        /// <summary>
        /// Create a processor with quick test configuration
        /// </summary>
        public static UnifiedAmazonProcessor CreateForTesting(string geminiApiKey, string associateTag = "test-tag-20")
        {
            var config = AmazonScrapingConfiguration.CreateTestConfiguration(geminiApiKey, associateTag);
            return new UnifiedAmazonProcessor(config);
        }

        /// <summary>
        /// Create a processor with production configuration
        /// </summary>
        public static UnifiedAmazonProcessor CreateForProduction(string geminiApiKey, string associateTag)
        {
            var config = AmazonScrapingConfiguration.CreateProductionConfiguration(geminiApiKey, associateTag);
            return new UnifiedAmazonProcessor(config);
        }

        /// <summary>
        /// Process from existing JSON file
        /// </summary>
        public async Task<ProcessingResult> ProcessFromJsonAsync(string jsonFilePath)
        {
            var result = new ProcessingResult
            {
                OutputDirectory = _config.OutputDirectory,
                StrategyUsed = _config.ExtractionStrategy
            };

            try
            {
                Console.WriteLine($"📂 Loading products from JSON: {jsonFilePath}");

                if (!File.Exists(jsonFilePath))
                {
                    result.AddError($"JSON file not found: {jsonFilePath}");
                    return result;
                }

                var json = await File.ReadAllTextAsync(jsonFilePath);
                var products = System.Text.Json.JsonSerializer.Deserialize<List<ProductInfo>>(json) ?? new List<ProductInfo>();

                result.ProductsFound = products.Count;
                Console.WriteLine($"✅ Loaded {products.Count} products from JSON");

                // Generate affiliate links if missing
                await GenerateAffiliateLinks(products);

                // Generate Facebook posts if requested
                if (_config.GeneratePosts)
                {
                    var postsGenerated = await GenerateFacebookPosts(products);
                    result.PostsGenerated = postsGenerated;
                }

                result.Success = true;
                Console.WriteLine("🎉 JSON processing completed successfully!");
            }
            catch (Exception ex)
            {
                result.AddError($"JSON processing failed: {ex.Message}");
                Console.WriteLine($"❌ JSON processing failed: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Quick preview of products without generating posts
        /// </summary>
        public async Task<List<ProductInfo>> PreviewProductsAsync()
        {
            Console.WriteLine("👀 Preview mode: Extracting products without generating posts...");

            try
            {
                await InitializeBrowser();
                var products = await ExtractProducts();
                await GenerateAffiliateLinks(products);

                Console.WriteLine($"📊 Preview completed: {products.Count} products found");
                
                // Display summary
                foreach (var product in products.Take(10))
                {
                    Console.WriteLine($"   • {product.GetSummary()}");
                }

                if (products.Count > 10)
                {
                    Console.WriteLine($"   ... and {products.Count - 10} more products");
                }

                return products;
            }
            finally
            {
                await CleanupResources();
            }
        }

        /// <summary>
        /// Process a single page for quick testing
        /// </summary>
        public async Task<ProcessingResult> ProcessSinglePageAsync()
        {
            // Temporarily override configuration for single page processing
            var originalMaxPages = _config.MaxPages;
            var originalMaxClicks = _config.MaxLoadMoreClicks;
            
            try
            {
                // Use reflection to temporarily modify the configuration
                var maxPagesField = typeof(AmazonScrapingConfiguration).GetProperty(nameof(AmazonScrapingConfiguration.MaxPages));
                var maxClicksField = typeof(AmazonScrapingConfiguration).GetProperty(nameof(AmazonScrapingConfiguration.MaxLoadMoreClicks));
                
                maxPagesField?.SetValue(_config, 1);
                maxClicksField?.SetValue(_config, 5);

                Console.WriteLine("🧪 Single page test mode activated");
                return await ProcessAsync();
            }
            finally
            {
                // Restore original values
                var maxPagesField = typeof(AmazonScrapingConfiguration).GetProperty(nameof(AmazonScrapingConfiguration.MaxPages));
                var maxClicksField = typeof(AmazonScrapingConfiguration).GetProperty(nameof(AmazonScrapingConfiguration.MaxLoadMoreClicks));
                
                maxPagesField?.SetValue(_config, originalMaxPages);
                maxClicksField?.SetValue(_config, originalMaxClicks);
            }
        }
    }
}
