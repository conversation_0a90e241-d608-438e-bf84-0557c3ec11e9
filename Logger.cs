using System.Collections.Concurrent;
using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Centralized logging system with structured logging and error reporting
    /// </summary>
    public static class Logger
    {
        private static readonly ConcurrentQueue<LogEntry> _logQueue = new();
        private static readonly Timer _flushTimer;
        private static string _logDirectory = "";
        private static bool _consoleLoggingEnabled = true;

        static Logger()
        {
            _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// Initialize logger with output directory
        /// </summary>
        public static void Initialize(string outputDirectory, bool enableConsoleLogging = true)
        {
            _logDirectory = Path.Combine(outputDirectory, "logs");
            _consoleLoggingEnabled = enableConsoleLogging;
            Directory.CreateDirectory(_logDirectory);
        }

        /// <summary>
        /// Log information message
        /// </summary>
        public static void LogInfo(string message, string? category = null, object? data = null)
        {
            Log(LogLevel.Info, message, category, data);
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        public static void LogWarning(string message, string? category = null, object? data = null)
        {
            Log(LogLevel.Warning, message, category, data);
        }

        /// <summary>
        /// Log error message
        /// </summary>
        public static void LogError(string message, Exception? exception = null, string? category = null, object? data = null)
        {
            var logData = data ?? new { };
            if (exception != null)
            {
                logData = new
                {
                    Data = data,
                    Exception = new
                    {
                        exception.Message,
                        exception.StackTrace,
                        Type = exception.GetType().Name
                    }
                };
            }

            Log(LogLevel.Error, message, category, logData);
        }

        /// <summary>
        /// Log debug message (only in debug builds)
        /// </summary>
        public static void LogDebug(string message, string? category = null, object? data = null)
        {
#if DEBUG
            Log(LogLevel.Debug, message, category, data);
#endif
        }

        /// <summary>
        /// Log performance metrics
        /// </summary>
        public static void LogPerformance(string operation, TimeSpan duration, object? metrics = null)
        {
            var data = new
            {
                Operation = operation,
                DurationMs = duration.TotalMilliseconds,
                Metrics = metrics
            };

            Log(LogLevel.Performance, $"Performance: {operation} took {duration.TotalMilliseconds:F2}ms", "Performance", data);
        }

        /// <summary>
        /// Log processing progress
        /// </summary>
        public static void LogProgress(string operation, int current, int total, object? additionalData = null)
        {
            var percentage = total > 0 ? (current * 100.0 / total) : 0;
            var data = new
            {
                Operation = operation,
                Current = current,
                Total = total,
                Percentage = percentage,
                AdditionalData = additionalData
            };

            Log(LogLevel.Progress, $"Progress: {operation} {current}/{total} ({percentage:F1}%)", "Progress", data);
        }

        /// <summary>
        /// Core logging method
        /// </summary>
        private static void Log(LogLevel level, string message, string? category, object? data)
        {
            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = level,
                Message = message,
                Category = category ?? "General",
                Data = data,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);

            // Console output for immediate feedback
            if (_consoleLoggingEnabled)
            {
                WriteToConsole(entry);
            }
        }

        /// <summary>
        /// Write log entry to console with color coding
        /// </summary>
        private static void WriteToConsole(LogEntry entry)
        {
            var originalColor = Console.ForegroundColor;
            
            try
            {
                Console.ForegroundColor = GetConsoleColor(entry.Level);
                var prefix = GetLevelPrefix(entry.Level);
                var timestamp = entry.Timestamp.ToString("HH:mm:ss");
                
                if (!string.IsNullOrEmpty(entry.Category) && entry.Category != "General")
                {
                    Console.WriteLine($"[{timestamp}] {prefix} [{entry.Category}] {entry.Message}");
                }
                else
                {
                    Console.WriteLine($"[{timestamp}] {prefix} {entry.Message}");
                }
            }
            finally
            {
                Console.ForegroundColor = originalColor;
            }
        }

        /// <summary>
        /// Get console color for log level
        /// </summary>
        private static ConsoleColor GetConsoleColor(LogLevel level)
        {
            return level switch
            {
                LogLevel.Error => ConsoleColor.Red,
                LogLevel.Warning => ConsoleColor.Yellow,
                LogLevel.Info => ConsoleColor.White,
                LogLevel.Debug => ConsoleColor.Gray,
                LogLevel.Performance => ConsoleColor.Cyan,
                LogLevel.Progress => ConsoleColor.Green,
                _ => ConsoleColor.White
            };
        }

        /// <summary>
        /// Get prefix for log level
        /// </summary>
        private static string GetLevelPrefix(LogLevel level)
        {
            return level switch
            {
                LogLevel.Error => "❌",
                LogLevel.Warning => "⚠️",
                LogLevel.Info => "ℹ️",
                LogLevel.Debug => "🐛",
                LogLevel.Performance => "⏱️",
                LogLevel.Progress => "📊",
                _ => "📝"
            };
        }

        /// <summary>
        /// Flush logs to file
        /// </summary>
        private static void FlushLogs(object? state)
        {
            if (string.IsNullOrEmpty(_logDirectory) || _logQueue.IsEmpty)
                return;

            var entries = new List<LogEntry>();
            while (_logQueue.TryDequeue(out var entry))
            {
                entries.Add(entry);
            }

            if (entries.Count == 0) return;

            try
            {
                var logFile = Path.Combine(_logDirectory, $"log_{DateTime.UtcNow:yyyyMMdd}.json");
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var existingEntries = new List<LogEntry>();
                if (File.Exists(logFile))
                {
                    var existingJson = File.ReadAllText(logFile);
                    existingEntries = JsonSerializer.Deserialize<List<LogEntry>>(existingJson, jsonOptions) ?? new List<LogEntry>();
                }

                existingEntries.AddRange(entries);
                var json = JsonSerializer.Serialize(existingEntries, jsonOptions);
                File.WriteAllText(logFile, json);
            }
            catch (Exception ex)
            {
                // Fallback to console if file logging fails
                Console.WriteLine($"❌ Failed to write logs to file: {ex.Message}");
            }
        }

        /// <summary>
        /// Create error report for critical failures
        /// </summary>
        public static async Task CreateErrorReportAsync(Exception exception, string operation, object? context = null)
        {
            try
            {
                var report = new ErrorReport
                {
                    Timestamp = DateTime.UtcNow,
                    Operation = operation,
                    Exception = new ExceptionInfo
                    {
                        Message = exception.Message,
                        StackTrace = exception.StackTrace ?? "",
                        Type = exception.GetType().FullName ?? "",
                        InnerException = exception.InnerException?.Message
                    },
                    Context = context,
                    SystemInfo = new SystemInfo
                    {
                        OSVersion = Environment.OSVersion.ToString(),
                        RuntimeVersion = Environment.Version.ToString(),
                        MachineName = Environment.MachineName,
                        UserName = Environment.UserName,
                        WorkingDirectory = Environment.CurrentDirectory
                    }
                };

                var reportFile = Path.Combine(_logDirectory, $"error_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json");
                var json = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(reportFile, json);

                LogError($"Error report created: {reportFile}", exception, "ErrorReporting");
            }
            catch (Exception reportEx)
            {
                Console.WriteLine($"❌ Failed to create error report: {reportEx.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public static void Shutdown()
        {
            _flushTimer?.Dispose();
            FlushLogs(null); // Final flush
        }
    }

    /// <summary>
    /// Log levels
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Performance,
        Progress
    }

    /// <summary>
    /// Log entry structure
    /// </summary>
    public class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; } = "";
        public string Category { get; set; } = "";
        public object? Data { get; set; }
        public int ThreadId { get; set; }
    }

    /// <summary>
    /// Error report structure
    /// </summary>
    public class ErrorReport
    {
        public DateTime Timestamp { get; set; }
        public string Operation { get; set; } = "";
        public ExceptionInfo Exception { get; set; } = new();
        public object? Context { get; set; }
        public SystemInfo SystemInfo { get; set; } = new();
    }

    public class ExceptionInfo
    {
        public string Message { get; set; } = "";
        public string StackTrace { get; set; } = "";
        public string Type { get; set; } = "";
        public string? InnerException { get; set; }
    }

    public class SystemInfo
    {
        public string OSVersion { get; set; } = "";
        public string RuntimeVersion { get; set; } = "";
        public string MachineName { get; set; } = "";
        public string UserName { get; set; } = "";
        public string WorkingDirectory { get; set; } = "";
    }
}
