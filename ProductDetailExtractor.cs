using System;
using System.Threading.Tasks;
using PuppeteerSharp;
using System.Text.RegularExpressions;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur spécialisé pour les détails complets des produits Amazon
    /// Gère le scroll complet de la page et l'extraction du lien d'affiliation
    /// </summary>
    public class ProductDetailExtractor
    {
        private readonly Random _random;

        public ProductDetailExtractor()
        {
            _random = new Random();
        }

        /// <summary>
        /// Extrait tous les détails d'un produit en scrollant la page complètement
        /// et en récupérant le vrai lien d'affiliation
        /// </summary>
        public async Task<ProductInfo> ExtractCompleteProductDetails(IPage page, ProductInfo basicProduct, string amazonAssociateTag)
        {
            try
            {
                Console.WriteLine($"🔍 Extraction complète pour: {basicProduct.Title.Substring(0, Math.Min(50, basicProduct.Title.Length))}...");

                // Vérifier que la page n'est pas fermée
                if (page.IsClosed)
                {
                    Console.WriteLine("   ⚠️ Page fermée, impossible d'extraire les détails");
                    return basicProduct;
                }

                // Attendre le chargement initial (la navigation est déjà faite par AmazonLoader)
                Console.WriteLine("   ⏳ Attente du chargement de la page...");
                try
                {
                    // Attendre que le DOM soit chargé
                    await page.WaitForSelectorAsync("body", new WaitForSelectorOptions { Timeout = 10000 });
                    await Task.Delay(1000);
                }
                catch (WaitTaskTimeoutException)
                {
                    Console.WriteLine("   ⚠️ Timeout lors du chargement, tentative d'extraction directe");
                }

                // Vérifier que nous sommes sur une page produit Amazon
                var isProductPage = await page.EvaluateFunctionAsync<bool>(@"() => {
                    return document.title.includes('Amazon') &&
                           (document.querySelector('#productTitle') ||
                            document.querySelector('.product-title') ||
                            document.querySelector('[data-feature-name=""title""]')) !== null;
                }");

                if (!isProductPage)
                {
                    Console.WriteLine("   ⚠️ Page produit non détectée, tentative d'extraction directe");
                }

                // Scroll complet de la page pour charger tout le contenu
                await ScrollCompletelyThroughPage(page);

                // Extraire les détails du produit
                var details = await ExtractProductDetails(page);

                // Mettre à jour le produit avec les détails extraits
                UpdateProductWithDetails(basicProduct, details);

                // Récupérer le taux de commission et le lien d'affiliation
                var commissionRate = await GetCommissionRate(page);
                if (!string.IsNullOrEmpty(commissionRate))
                {
                    basicProduct.CommissionRate = commissionRate;
                    Console.WriteLine($"   💰 Taux de commission: {commissionRate}");
                }

                var affiliateLink = await GetAffiliateLink(page, amazonAssociateTag);
                if (!string.IsNullOrEmpty(affiliateLink))
                {
                    basicProduct.AffiliateLink = affiliateLink;
                }
                else
                {
                    // Fallback : générer le lien d'affiliation basique
                    basicProduct.AffiliateLink = GenerateBasicAffiliateLink(basicProduct.ProductUrl, amazonAssociateTag);
                }

                Console.WriteLine($"   ✅ Extraction complète terminée");
                return basicProduct;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Erreur extraction détails : {ex.Message}");
                // En cas d'erreur, retourner le produit avec lien d'affiliation basique
                basicProduct.AffiliateLink = GenerateBasicAffiliateLink(basicProduct.ProductUrl, amazonAssociateTag);
                return basicProduct;
            }
        }

        /// <summary>
        /// Scroll complet à travers toute la page pour charger le contenu dynamique
        /// </summary>
        private async Task ScrollCompletelyThroughPage(IPage page)
        {
            try
            {
                Console.WriteLine("   📜 Scroll complet de la page...");

                await page.EvaluateFunctionAsync(@"
                    async () => {
                        // Fonction pour attendre
                        const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

                        // Scroll simple et efficace
                        const scrollStep = 800;
                        const scrollDelay = 300;
                        const maxScrolls = 10; // Limite fixe pour éviter les boucles infinies

                        console.log('Début du scroll simple');

                        // Scroll progressif par étapes fixes
                        for (let i = 0; i < maxScrolls; i++) {
                            const scrollPosition = i * scrollStep;
                            window.scrollTo(0, scrollPosition);
                            console.log(`Scroll ${i + 1}/${maxScrolls} - Position: ${scrollPosition}px`);
                            await wait(scrollDelay);
                        }

                        // Scroll final jusqu'en bas de la page
                        const finalHeight = Math.max(
                            document.body.scrollHeight,
                            document.documentElement.scrollHeight
                        );

                        window.scrollTo(0, finalHeight);
                        console.log(`Scroll final vers: ${finalHeight}px`);
                        await wait(2000);

                        // Retourner en haut pour faciliter l'extraction
                        window.scrollTo(0, 0);
                        await wait(500);

                        console.log('Scroll terminé, retour en haut');
                    }");

                Console.WriteLine("   ✅ Scroll complet terminé");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Erreur lors du scroll : {ex.Message}");
            }
        }

        /// <summary>
        /// Extrait tous les détails disponibles du produit
        /// </summary>
        private async Task<ProductDetails> ExtractProductDetails(IPage page)
        {
            try
            {
                var details = await page.EvaluateFunctionAsync<ProductDetails>(@"() => {
                    const details = {
                        price: '',
                        originalPrice: '',
                        rating: '',
                        reviewCount: '',
                        imageUrl: '',
                        availability: '',
                        prime: false,
                        freeShipping: false,
                        description: '',
                        features: []
                    };

                    // Prix actuel
                    const priceSelectors = [
                        '.a-price .a-offscreen',
                        '.a-price-whole',
                        '#priceblock_dealprice',
                        '#priceblock_ourprice',
                        '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen'
                    ];
                    
                    for (const selector of priceSelectors) {
                        const priceEl = document.querySelector(selector);
                        if (priceEl && priceEl.textContent.trim()) {
                            details.price = priceEl.textContent.trim();
                            break;
                        }
                    }

                    // Prix original (barré)
                    const originalPriceSelectors = [
                        '.a-text-price .a-offscreen',
                        '.a-price.a-text-price .a-offscreen',
                        '[data-cy=""list-price""] .a-offscreen',
                        '.a-text-strike .a-offscreen'
                    ];
                    
                    for (const selector of originalPriceSelectors) {
                        const origPriceEl = document.querySelector(selector);
                        if (origPriceEl && origPriceEl.textContent.trim()) {
                            details.originalPrice = origPriceEl.textContent.trim();
                            break;
                        }
                    }

                    // Note
                    const ratingSelectors = [
                        '.a-icon-alt',
                        '[data-hook=""average-star-rating""] .a-icon-alt',
                        '.a-star-medium .a-icon-alt'
                    ];
                    
                    for (const selector of ratingSelectors) {
                        const ratingEl = document.querySelector(selector);
                        if (ratingEl && ratingEl.textContent.includes('étoiles')) {
                            details.rating = ratingEl.textContent.trim();
                            break;
                        }
                    }

                    // Nombre d'avis
                    const reviewSelectors = [
                        '#acrCustomerReviewText',
                        '[data-hook=""total-review-count""]',
                        '.a-link-normal[href*=""#customerReviews""]'
                    ];
                    
                    for (const selector of reviewSelectors) {
                        const reviewEl = document.querySelector(selector);
                        if (reviewEl && reviewEl.textContent.trim()) {
                            details.reviewCount = reviewEl.textContent.trim();
                            break;
                        }
                    }

                    // Image principale
                    const imageSelectors = [
                        '#landingImage',
                        '.a-dynamic-image',
                        '#imgTagWrapperId img'
                    ];
                    
                    for (const selector of imageSelectors) {
                        const imageEl = document.querySelector(selector);
                        if (imageEl) {
                            details.imageUrl = imageEl.src || imageEl.getAttribute('data-src') || imageEl.getAttribute('data-old-hires');
                            if (details.imageUrl) break;
                        }
                    }

                    // Disponibilité
                    const availSelectors = [
                        '#availability span',
                        '.a-color-success',
                        '.a-color-state'
                    ];
                    
                    for (const selector of availSelectors) {
                        const availEl = document.querySelector(selector);
                        if (availEl && availEl.textContent.trim()) {
                            details.availability = availEl.textContent.trim();
                            break;
                        }
                    }

                    // Prime
                    details.prime = !!document.querySelector('.a-icon-prime, [data-csa-c-element-id=""prime-logo""], .prime-logo');

                    // Livraison gratuite
                    const shippingElements = document.querySelectorAll('[data-feature-name=""deliveryMessage""], .a-color-secondary, .a-color-base');
                    for (const el of shippingElements) {
                        if (el.textContent && el.textContent.toLowerCase().includes('gratuit')) {
                            details.freeShipping = true;
                            break;
                        }
                    }

                    // Description courte
                    const descSelectors = [
                        '#feature-bullets ul',
                        '.a-unordered-list.a-vertical.a-spacing-mini',
                        '#productDescription'
                    ];
                    
                    for (const selector of descSelectors) {
                        const descEl = document.querySelector(selector);
                        if (descEl && descEl.textContent.trim()) {
                            details.description = descEl.textContent.trim().substring(0, 500);
                            break;
                        }
                    }

                    return details;
                }");

                return details;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Erreur extraction détails : {ex.Message}");
                return new ProductDetails();
            }
        }

        /// <summary>
        /// Extrait le taux de commission affiché sur la page
        /// </summary>
        private async Task<string> GetCommissionRate(IPage page)
        {
            try
            {
                Console.WriteLine("   💰 Recherche du taux de commission...");

                var commissionRate = await page.EvaluateFunctionAsync<string>(@"() => {
                    // Fonction pour chercher du texte contenant un pourcentage
                    function findCommissionInText() {
                        const allElements = document.querySelectorAll('*');
                        for (const element of allElements) {
                            const text = element.textContent || '';

                            // Chercher des patterns comme '5,00%', '5.00%', '5%'
                            const commissionMatch = text.match(/(\d+[,.]?\d*)\s*%/);
                            if (commissionMatch) {
                                // Vérifier si c'est dans un contexte de commission
                                const contextText = text.toLowerCase();
                                if (contextText.includes('commission') ||
                                    contextText.includes('taux') ||
                                    contextText.includes('rate') ||
                                    contextText.includes('affiliate') ||
                                    contextText.includes('associates')) {
                                    return commissionMatch[0];
                                }

                                // Vérifier si l'élément parent contient des mots-clés de commission
                                let parent = element.parentElement;
                                while (parent && parent !== document.body) {
                                    const parentText = parent.textContent || '';
                                    const parentTextLower = parentText.toLowerCase();
                                    if (parentTextLower.includes('commission') ||
                                        parentTextLower.includes('taux') ||
                                        parentTextLower.includes('rate')) {
                                        return commissionMatch[0];
                                    }
                                    parent = parent.parentElement;
                                }
                            }
                        }
                        return '';
                    }

                    // Essayer la recherche par texte
                    const commissionFromText = findCommissionInText();
                    if (commissionFromText) {
                        return commissionFromText;
                    }

                    // Essayer les sélecteurs spécifiques
                    const specificSelectors = [
                        '.commission-rate',
                        '[data-commission]',
                        '.rate-display',
                        '[class*=""commission""]',
                        '[class*=""rate""]'
                    ];

                    for (const selector of specificSelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            for (const element of elements) {
                                const text = element.textContent || '';
                                const match = text.match(/(\d+[,.]?\d*)\s*%/);
                                if (match) {
                                    return match[0];
                                }
                            }
                        } catch (e) {
                            // Continuer avec le sélecteur suivant
                        }
                    }

                    return '';
                }");

                if (!string.IsNullOrEmpty(commissionRate))
                {
                    Console.WriteLine($"   ✅ Taux de commission trouvé: {commissionRate}");
                    return commissionRate;
                }
                else
                {
                    Console.WriteLine("   ⚠️ Taux de commission non trouvé");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Erreur extraction taux de commission : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Récupère le lien d'affiliation en cliquant sur "Obtenir un lien"
        /// </summary>
        private async Task<string> GetAffiliateLink(IPage page, string amazonAssociateTag)
        {
            try
            {
                Console.WriteLine("   🔗 Recherche du bouton 'Obtenir un lien'...");

                // Chercher le bouton "Obtenir le lien" ou équivalent
                var affiliateButtonSelectors = new[]
                {
                    "[data-action='associate-link']",
                    ".associate-link-button",
                    "button[class*='associate']",
                    "a[class*='associate']"
                };

                IElementHandle? affiliateButton = null;

                // D'abord essayer les sélecteurs CSS spécifiques
                foreach (var selector in affiliateButtonSelectors)
                {
                    try
                    {
                        affiliateButton = await page.QuerySelectorAsync(selector);
                        if (affiliateButton != null)
                        {
                            var isVisible = await affiliateButton.IsVisibleAsync();
                            if (isVisible)
                            {
                                Console.WriteLine($"   ✅ Bouton trouvé avec sélecteur: {selector}");
                                break;
                            }
                        }
                    }
                    catch
                    {
                        // Continuer avec le sélecteur suivant
                    }
                }

                // Si pas trouvé, chercher dans tous les boutons et liens
                if (affiliateButton == null)
                {
                    var allButtons = await page.QuerySelectorAllAsync("button, a");

                    foreach (var button in allButtons)
                    {
                        try
                        {
                            var buttonText = await button.GetPropertyAsync("textContent");
                            var text = await buttonText.JsonValueAsync<string>();

                            if (text != null && (text.Contains("Obtenir un lien") || text.Contains("Obtenir le lien") ||
                                               text.Contains("Get link") || text.Contains("Associate") || text.Contains("Affiliate")))
                            {
                                var isVisible = await button.IsVisibleAsync();
                                if (isVisible)
                                {
                                    Console.WriteLine($"   ✅ Bouton trouvé par texte: {text.Trim()}");
                                    affiliateButton = button;
                                    break;
                                }
                            }
                        }
                        catch
                        {
                            // Continuer avec le bouton suivant
                        }
                    }
                }

                if (affiliateButton != null)
                {
                    Console.WriteLine("   🖱️ Clic sur le bouton 'Obtenir le lien'...");
                    await affiliateButton.ClickAsync();
                    
                    // Attendre que le lien soit généré
                    await Task.Delay(2000);
                    
                    // Chercher le lien d'affiliation généré
                    var affiliateLink = await page.EvaluateFunctionAsync<string>(@"() => {
                        // Chercher dans les champs de texte ou zones de lien
                        const linkSelectors = [
                            'input[value*=""tag=""]',
                            'textarea[value*=""tag=""]',
                            '.affiliate-link',
                            '[data-affiliate-link]'
                        ];
                        
                        for (const selector of linkSelectors) {
                            const element = document.querySelector(selector);
                            if (element && element.value && element.value.includes('tag=')) {
                                return element.value;
                            }
                        }
                        
                        return '';
                    }");

                    if (!string.IsNullOrEmpty(affiliateLink))
                    {
                        Console.WriteLine("   ✅ Lien d'affiliation récupéré via bouton");
                        return affiliateLink;
                    }
                }

                Console.WriteLine("   ⚠️ Bouton 'Obtenir le lien' non trouvé, génération basique");
                return "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Erreur récupération lien d'affiliation : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Génère un lien d'affiliation basique à partir de l'URL du produit
        /// </summary>
        private string GenerateBasicAffiliateLink(string productUrl, string amazonAssociateTag)
        {
            try
            {
                var asinMatch = Regex.Match(productUrl, @"/dp/([A-Z0-9]{10})");
                if (asinMatch.Success)
                {
                    var asin = asinMatch.Groups[1].Value;
                    return $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                }
                
                // Si pas d'ASIN trouvé, ajouter le tag à l'URL existante
                var separator = productUrl.Contains("?") ? "&" : "?";
                return $"{productUrl}{separator}tag={amazonAssociateTag}";
            }
            catch
            {
                return productUrl; // Fallback sur l'URL originale
            }
        }

        /// <summary>
        /// Met à jour le produit avec les détails extraits
        /// </summary>
        private void UpdateProductWithDetails(ProductInfo product, ProductDetails details)
        {
            if (!string.IsNullOrEmpty(details.Price))
                product.Price = details.Price;
            if (!string.IsNullOrEmpty(details.OriginalPrice))
                product.OriginalPrice = details.OriginalPrice;
            if (!string.IsNullOrEmpty(details.Rating))
                product.Rating = details.Rating;
            if (!string.IsNullOrEmpty(details.ReviewCount))
                product.ReviewCount = details.ReviewCount;
            if (!string.IsNullOrEmpty(details.ImageUrl))
                product.ImageUrl = details.ImageUrl;
        }
    }

    /// <summary>
    /// Détails complets extraits d'une page produit
    /// </summary>
    public class ProductDetails
    {
        public string Price { get; set; } = "";
        public string OriginalPrice { get; set; } = "";
        public string Rating { get; set; } = "";
        public string ReviewCount { get; set; } = "";
        public string ImageUrl { get; set; } = "";
        public string Availability { get; set; } = "";
        public bool Prime { get; set; }
        public bool FreeShipping { get; set; }
        public string Description { get; set; } = "";
        public string[] Features { get; set; } = new string[0];
    }
}
