using System.Diagnostics;
using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Unified base processor that consolidates common functionality
    /// </summary>
    public abstract class BaseAmazonProcessor : IDisposable
    {
        protected readonly AmazonScrapingConfiguration _config;
        protected readonly AmazonLoader<string> _amazonLoader;
        protected readonly FacebookPostGenerator _postGenerator;
        protected readonly List<ProductInfo> _extractedProducts;
        protected readonly Random _random;
        protected bool _disposed = false;

        protected BaseAmazonProcessor(AmazonScrapingConfiguration config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            
            var validationResult = _config.Validate();
            if (validationResult != System.ComponentModel.DataAnnotations.ValidationResult.Success)
            {
                throw new ArgumentException(validationResult.ErrorMessage, nameof(config));
            }

            _amazonLoader = new AmazonLoader<string>();
            _postGenerator = new FacebookPostGenerator(_config.GeminiApiKey);
            _extractedProducts = new List<ProductInfo>();
            _random = new Random();

            // Configure components
            _amazonLoader.SetGeminiApiKey(_config.GeminiApiKey);
            
            // Ensure output directory exists
            Directory.CreateDirectory(_config.OutputDirectory);
        }

        /// <summary>
        /// Main processing workflow - template method pattern
        /// </summary>
        public async Task<ProcessingResult> ProcessAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ProcessingResult
            {
                OutputDirectory = _config.OutputDirectory,
                StrategyUsed = _config.ExtractionStrategy
            };

            try
            {
                Console.WriteLine("🚀 Starting Amazon scraping process...");
                Console.WriteLine($"📊 Configuration: {_config.MaxPages} pages, {_config.ExtractionStrategy} strategy");
                Console.WriteLine();

                // Step 1: Initialize browser
                await InitializeBrowser();

                // Step 2: Extract products (implemented by derived classes)
                var products = await ExtractProducts();
                result.ProductsFound = products.Count;

                if (products.Count == 0)
                {
                    result.AddWarning("No products found during extraction");
                    return result;
                }

                Console.WriteLine($"✅ {products.Count} products extracted successfully");

                // Step 3: Generate affiliate links
                await GenerateAffiliateLinks(products);

                // Step 4: Save products JSON if requested
                if (_config.SaveProductsJson)
                {
                    await SaveProductsToJson(products);
                }

                // Step 5: Generate Facebook posts if requested
                if (_config.GeneratePosts)
                {
                    var postsGenerated = await GenerateFacebookPosts(products);
                    result.PostsGenerated = postsGenerated;
                }

                result.Success = true;
                Console.WriteLine("🎉 Processing completed successfully!");
            }
            catch (Exception ex)
            {
                result.AddError($"Processing failed: {ex.Message}");
                Console.WriteLine($"❌ Processing failed: {ex.Message}");
            }
            finally
            {
                await CleanupResources();
                stopwatch.Stop();
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            }

            return result;
        }

        /// <summary>
        /// Abstract method for product extraction - implemented by derived classes
        /// </summary>
        protected abstract Task<List<ProductInfo>> ExtractProducts();

        /// <summary>
        /// Initialize browser with optimized settings
        /// </summary>
        protected virtual async Task InitializeBrowser()
        {
            Console.WriteLine("🌐 Initializing browser...");
            await _amazonLoader.StartBrowser(_config.HeadlessMode);
        }

        /// <summary>
        /// Generate affiliate links for all products
        /// </summary>
        protected virtual async Task GenerateAffiliateLinks(List<ProductInfo> products)
        {
            Console.WriteLine("🔗 Generating affiliate links...");
            
            await Task.Run(() =>
            {
                foreach (var product in products.Where(p => string.IsNullOrEmpty(p.AffiliateLink)))
                {
                    if (!string.IsNullOrEmpty(product.ASIN))
                    {
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{product.ASIN}?tag={_config.AmazonAssociateTag}";
                    }
                }
            });

            Console.WriteLine($"✅ Affiliate links generated for {products.Count} products");
        }

        /// <summary>
        /// Save products to JSON file
        /// </summary>
        protected virtual async Task SaveProductsToJson(List<ProductInfo> products)
        {
            var jsonPath = Path.Combine(_config.OutputDirectory, $"products_{DateTime.Now:yyyyMMdd_HHmmss}.json");
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(products, options);
            await File.WriteAllTextAsync(jsonPath, json);
            
            Console.WriteLine($"💾 Products saved to: {jsonPath}");
        }

        /// <summary>
        /// Generate Facebook posts for all products
        /// </summary>
        protected virtual async Task<int> GenerateFacebookPosts(List<ProductInfo> products)
        {
            Console.WriteLine($"📝 Generating Facebook posts for {products.Count} products...");
            
            var successCount = 0;
            var validProducts = products.Where(p => p.IsValid()).ToList();

            for (int i = 0; i < validProducts.Count; i++)
            {
                var product = validProducts[i];
                
                try
                {
                    Console.WriteLine($"📝 Generating post {i + 1}/{validProducts.Count}: {product.GetSummary()}");
                    
                    var post = await _postGenerator.GeneratePostFromProductInfo(product);
                    product.FacebookPost = post;

                    // Save individual post file
                    await SaveIndividualPost(product, i + 1);
                    
                    successCount++;
                    
                    // Add delay between posts to avoid rate limiting
                    if (i < validProducts.Count - 1)
                    {
                        await Task.Delay(_config.DelayBetweenPosts);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed to generate post for {product.Title}: {ex.Message}");
                }
            }

            Console.WriteLine($"✅ {successCount}/{validProducts.Count} Facebook posts generated successfully");
            return successCount;
        }

        /// <summary>
        /// Save individual post to file
        /// </summary>
        protected virtual async Task SaveIndividualPost(ProductInfo product, int index)
        {
            var sanitizedTitle = SanitizeFileName(product.Title[..Math.Min(30, product.Title.Length)]);
            var fileName = $"post_{index:D3}_{sanitizedTitle}.txt";
            var filePath = Path.Combine(_config.OutputDirectory, fileName);

            var content = CreatePostFileContent(product);
            await File.WriteAllTextAsync(filePath, content);
        }

        /// <summary>
        /// Create content for post file
        /// </summary>
        protected virtual string CreatePostFileContent(ProductInfo product)
        {
            return $"""
                Product: {product.Title}
                URL: {product.ProductUrl}
                Affiliate Link: {product.AffiliateLink}
                Price: {product.Price}
                Discount: {product.Discount}
                Rating: {product.Rating}
                Commission Rate: {product.CommissionRate}
                Category: {product.Category}
                Extracted At: {product.ExtractedAt:yyyy-MM-dd HH:mm:ss}

                === FACEBOOK POST ===

                {product.FacebookPost}
                """;
        }

        /// <summary>
        /// Sanitize filename for safe file operations
        /// </summary>
        protected static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        protected virtual async Task CleanupResources()
        {
            try
            {
                await _amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Warning during cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose pattern implementation
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                CleanupResources().GetAwaiter().GetResult();
                _disposed = true;
            }
        }
    }
}
