using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Unified configuration class for all processing options
    /// </summary>
    public class AmazonScrapingConfiguration
    {
        [Range(1, 20)]
        [JsonPropertyName("maxPages")]
        public int MaxPages { get; set; } = 3;

        [Range(1, 50)]
        [JsonPropertyName("maxLoadMoreClicks")]
        public int MaxLoadMoreClicks { get; set; } = 20;

        [Required]
        [JsonPropertyName("amazonAssociateTag")]
        public string AmazonAssociateTag { get; set; } = "default-tag-20";

        [Required]
        [JsonPropertyName("geminiApiKey")]
        public string GeminiApiKey { get; set; } = "";

        [JsonPropertyName("headlessMode")]
        public bool HeadlessMode { get; set; } = false;

        [JsonPropertyName("outputDirectory")]
        public string OutputDirectory { get; set; } = "";

        [JsonPropertyName("saveProductsJson")]
        public bool SaveProductsJson { get; set; } = true;

        [JsonPropertyName("generatePosts")]
        public bool GeneratePosts { get; set; } = true;

        [Range(500, 10000)]
        [JsonPropertyName("delayBetweenPosts")]
        public int DelayBetweenPosts { get; set; } = 2000;

        [Range(1000, 30000)]
        [JsonPropertyName("pageLoadTimeout")]
        public int PageLoadTimeout { get; set; } = 15000;

        [Range(1, 10)]
        [JsonPropertyName("maxRetryAttempts")]
        public int MaxRetryAttempts { get; set; } = 3;

        [JsonPropertyName("extractionStrategy")]
        public ExtractionStrategy ExtractionStrategy { get; set; } = ExtractionStrategy.Hybrid;

        [JsonPropertyName("enableScreenshots")]
        public bool EnableScreenshots { get; set; } = true;

        [JsonPropertyName("enableAiFallback")]
        public bool EnableAiFallback { get; set; } = true;

        /// <summary>
        /// Validates the configuration
        /// </summary>
        public ValidationResult Validate()
        {
            var context = new ValidationContext(this);
            var results = new List<ValidationResult>();
            
            if (!Validator.TryValidateObject(this, context, results, true))
            {
                return new ValidationResult($"Configuration validation failed: {string.Join(", ", results.Select(r => r.ErrorMessage))}");
            }

            // Custom validations
            if (string.IsNullOrWhiteSpace(GeminiApiKey))
            {
                return new ValidationResult("Gemini API key is required");
            }

            if (string.IsNullOrWhiteSpace(OutputDirectory))
            {
                OutputDirectory = $"amazon_scraping_{DateTime.Now:yyyyMMdd_HHmmss}";
            }

            return ValidationResult.Success!;
        }

        /// <summary>
        /// Creates a default configuration for testing
        /// </summary>
        public static AmazonScrapingConfiguration CreateTestConfiguration(string geminiApiKey, string associateTag = "test-tag-20")
        {
            return new AmazonScrapingConfiguration
            {
                MaxPages = 1,
                MaxLoadMoreClicks = 5,
                AmazonAssociateTag = associateTag,
                GeminiApiKey = geminiApiKey,
                HeadlessMode = false,
                OutputDirectory = "test_output",
                GeneratePosts = true,
                SaveProductsJson = true,
                DelayBetweenPosts = 1000,
                ExtractionStrategy = ExtractionStrategy.Direct
            };
        }

        /// <summary>
        /// Creates a production configuration
        /// </summary>
        public static AmazonScrapingConfiguration CreateProductionConfiguration(string geminiApiKey, string associateTag)
        {
            return new AmazonScrapingConfiguration
            {
                MaxPages = 5,
                MaxLoadMoreClicks = 20,
                AmazonAssociateTag = associateTag,
                GeminiApiKey = geminiApiKey,
                HeadlessMode = true,
                OutputDirectory = $"production_output_{DateTime.Now:yyyyMMdd_HHmmss}",
                GeneratePosts = true,
                SaveProductsJson = true,
                DelayBetweenPosts = 2000,
                ExtractionStrategy = ExtractionStrategy.Hybrid,
                EnableScreenshots = true,
                EnableAiFallback = true
            };
        }
    }

    /// <summary>
    /// Extraction strategy enumeration
    /// </summary>
    public enum ExtractionStrategy
    {
        /// <summary>
        /// Use direct CSS selector-based scraping only
        /// </summary>
        Direct,
        
        /// <summary>
        /// Use AI-based extraction only
        /// </summary>
        AI,
        
        /// <summary>
        /// Use direct scraping with AI fallback (recommended)
        /// </summary>
        Hybrid
    }

    /// <summary>
    /// Processing result with detailed metrics
    /// </summary>
    public class ProcessingResult
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("productsFound")]
        public int ProductsFound { get; set; }

        [JsonPropertyName("postsGenerated")]
        public int PostsGenerated { get; set; }

        [JsonPropertyName("errors")]
        public List<string> Errors { get; set; } = new();

        [JsonPropertyName("warnings")]
        public List<string> Warnings { get; set; } = new();

        [JsonPropertyName("processingTimeMs")]
        public long ProcessingTimeMs { get; set; }

        [JsonPropertyName("outputDirectory")]
        public string OutputDirectory { get; set; } = "";

        [JsonPropertyName("extractionStrategy")]
        public ExtractionStrategy StrategyUsed { get; set; }

        [JsonPropertyName("completedAt")]
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Calculates success rate
        /// </summary>
        public double SuccessRate => ProductsFound > 0 ? (double)PostsGenerated / ProductsFound * 100 : 0;

        /// <summary>
        /// Adds an error to the result
        /// </summary>
        public void AddError(string error)
        {
            Errors.Add($"[{DateTime.Now:HH:mm:ss}] {error}");
        }

        /// <summary>
        /// Adds a warning to the result
        /// </summary>
        public void AddWarning(string warning)
        {
            Warnings.Add($"[{DateTime.Now:HH:mm:ss}] {warning}");
        }

        /// <summary>
        /// Creates a summary report
        /// </summary>
        public string GetSummaryReport()
        {
            var report = $"""
                🎯 PROCESSING SUMMARY
                ==================
                ✅ Success: {Success}
                📊 Products Found: {ProductsFound}
                📝 Posts Generated: {PostsGenerated}
                📈 Success Rate: {SuccessRate:F1}%
                ⏱️ Processing Time: {ProcessingTimeMs / 1000.0:F1}s
                🔧 Strategy Used: {StrategyUsed}
                📁 Output Directory: {OutputDirectory}
                
                """;

            if (Errors.Count > 0)
            {
                report += $"❌ Errors ({Errors.Count}):\n";
                report += string.Join("\n", Errors.Take(5));
                if (Errors.Count > 5) report += $"\n... and {Errors.Count - 5} more errors";
                report += "\n\n";
            }

            if (Warnings.Count > 0)
            {
                report += $"⚠️ Warnings ({Warnings.Count}):\n";
                report += string.Join("\n", Warnings.Take(3));
                if (Warnings.Count > 3) report += $"\n... and {Warnings.Count - 3} more warnings";
            }

            return report;
        }
    }
}
