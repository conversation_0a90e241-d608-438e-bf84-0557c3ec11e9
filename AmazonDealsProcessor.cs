using System.Text.Json;

namespace Amazon2FacebookPoster
{
    public class ProcessingOptions
    {
        public int MaxPages { get; set; } = 5;
        public string OutputDirectory { get; set; } = "facebook_posts";
        public string AmazonAssociateTag { get; set; } = "votre-tag-20";
        public bool SaveProductsJson { get; set; } = true;
        public bool GeneratePosts { get; set; } = true;
        public int DelayBetweenPosts { get; set; } = 2000; // ms
        public bool HeadlessMode { get; set; } = false;
    }

    internal class AmazonDealsProcessor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private readonly AmazonDealsExtractor _dealsExtractor;
        private readonly ProcessingOptions _options;

        public AmazonDealsProcessor(ProcessingOptions? options = null)
        {
            _amazonLoader = new AmazonLoader<string>();
            _dealsExtractor = new AmazonDealsExtractor(_amazonLoader);
            _options = options ?? new ProcessingOptions();
        }

        public void SetGeminiApiKey(string apiKey)
        {
            _amazonLoader.SetGeminiApiKey(apiKey);
            _dealsExtractor.SetGeminiApiKey(apiKey);
        }

        /// <summary>
        /// Processus complet : extraction des produits et génération des posts Facebook
        /// </summary>
        public async Task ProcessAmazonDeals()
        {
            try
            {
                Console.WriteLine("🚀 Démarrage du processus Amazon Deals to Facebook Posts");
                Console.WriteLine($"📊 Configuration :");
                Console.WriteLine($"   - Pages max : {_options.MaxPages}");
                Console.WriteLine($"   - Dossier de sortie : {_options.OutputDirectory}");
                Console.WriteLine($"   - Tag Amazon : {_options.AmazonAssociateTag}");
                Console.WriteLine($"   - Mode headless : {_options.HeadlessMode}");
                Console.WriteLine();

                // 1. Démarrer le navigateur
                Console.WriteLine("🌐 Démarrage du navigateur...");
                await _amazonLoader.StartBrowser(_options.HeadlessMode);

                // 2. Extraire tous les produits
                Console.WriteLine("🔍 Extraction des produits en cours...");
                var products = await _dealsExtractor.ExtractAllDealsProducts(_options.MaxPages);

                if (products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit trouvé. Vérifiez la page Amazon ou les sélecteurs.");
                    return;
                }

                Console.WriteLine($"✅ {products.Count} produits extraits avec succès !");

                // 3. Générer les liens d'affiliation
                Console.WriteLine("🔗 Génération des liens d'affiliation...");
                _dealsExtractor.GenerateAffiliateLinks(_options.AmazonAssociateTag);

                // 4. Sauvegarder les produits en JSON si demandé
                if (_options.SaveProductsJson)
                {
                    var jsonPath = Path.Combine(_options.OutputDirectory, "extracted_products.json");
                    await _dealsExtractor.SaveProductsToJson(jsonPath);
                }

                // 5. Générer les posts Facebook si demandé
                if (_options.GeneratePosts)
                {
                    Console.WriteLine("📝 Génération des posts Facebook...");
                    await _dealsExtractor.GenerateFacebookPostsForAllProducts(_options.OutputDirectory);
                }

                // 6. Générer un rapport de synthèse
                await GenerateSummaryReport(products);

                Console.WriteLine("🎉 Processus terminé avec succès !");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur durant le processus : {ex.Message}");
                Console.WriteLine($"Stack trace : {ex.StackTrace}");
            }
            finally
            {
                // 7. Fermer le navigateur
                Console.WriteLine("🔒 Fermeture du navigateur...");
                await _amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Génère un rapport de synthèse du processus
        /// </summary>
        private async Task GenerateSummaryReport(List<ProductInfo> products)
        {
            var reportPath = Path.Combine(_options.OutputDirectory, "processing_report.txt");
            
            var report = $"=== RAPPORT DE TRAITEMENT AMAZON DEALS ===\n";
            report += $"Date : {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
            report += $"Pages traitées : {_options.MaxPages}\n";
            report += $"Produits extraits : {products.Count}\n";
            report += $"Tag d'affiliation : {_options.AmazonAssociateTag}\n\n";

            report += "=== STATISTIQUES ===\n";
            var productsWithDiscount = products.Where(p => !string.IsNullOrEmpty(p.Discount)).Count();
            var productsWithRating = products.Where(p => !string.IsNullOrEmpty(p.Rating)).Count();
            var productsWithReviews = products.Where(p => !string.IsNullOrEmpty(p.ReviewCount)).Count();

            report += $"Produits avec réduction : {productsWithDiscount}\n";
            report += $"Produits avec note : {productsWithRating}\n";
            report += $"Produits avec avis : {productsWithReviews}\n\n";

            report += "=== TOP 10 PRODUITS ===\n";
            foreach (var product in products.Take(10))
            {
                report += $"- {product.Title}\n";
                report += $"  Prix : {product.Price} (était {product.OriginalPrice})\n";
                report += $"  Réduction : {product.Discount}\n";
                report += $"  URL : {product.ProductUrl}\n";
                report += $"  Affiliation : {product.AffiliateLink}\n\n";
            }

            if (!Directory.Exists(_options.OutputDirectory))
            {
                Directory.CreateDirectory(_options.OutputDirectory);
            }

            await File.WriteAllTextAsync(reportPath, report);
            Console.WriteLine($"📊 Rapport de synthèse généré : {reportPath}");
        }

        /// <summary>
        /// Traite seulement une page spécifique (pour les tests)
        /// </summary>
        public async Task ProcessSinglePage()
        {
            try
            {
                Console.WriteLine("🧪 Mode test - Traitement d'une seule page");
                
                await _amazonLoader.StartBrowser(_options.HeadlessMode);
                
                var products = await _dealsExtractor.ExtractAllDealsProducts(1);
                Console.WriteLine($"✅ {products.Count} produits extraits de la première page");

                if (products.Count > 0)
                {
                    _dealsExtractor.GenerateAffiliateLinks(_options.AmazonAssociateTag);
                    
                    // Générer seulement les 3 premiers posts pour le test
                    var testProducts = products.Take(3).ToList();
                    Console.WriteLine($"📝 Génération de {testProducts.Count} posts de test...");
                    
                    if (_options.GeneratePosts)
                    {
                        for (int i = 0; i < testProducts.Count; i++)
                        {
                            var product = testProducts[i];
                            try
                            {
                                Console.WriteLine($"📝 Test post {i + 1}/3 : {product.Title[..Math.Min(50, product.Title.Length)]}...");
                                var post = await _amazonLoader.GenerateFacebookPostFromUrl(product.ProductUrl, product.AffiliateLink);
                                
                                var fileName = $"test_post_{i + 1}.txt";
                                var filePath = Path.Combine(_options.OutputDirectory, fileName);
                                
                                if (!Directory.Exists(_options.OutputDirectory))
                                {
                                    Directory.CreateDirectory(_options.OutputDirectory);
                                }
                                
                                await File.WriteAllTextAsync(filePath, post);
                                Console.WriteLine($"✅ Post de test sauvegardé : {fileName}");
                                
                                await Task.Delay(1000);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"❌ Erreur pour le post de test {i + 1} : {ex.Message}");
                            }
                        }
                    }
                }
            }
            finally
            {
                await _amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Affiche un aperçu des produits extraits sans générer les posts
        /// </summary>
        public async Task PreviewProducts()
        {
            try
            {
                Console.WriteLine("👀 Mode aperçu - Extraction sans génération de posts");
                
                await _amazonLoader.StartBrowser(_options.HeadlessMode);
                
                var products = await _dealsExtractor.ExtractAllDealsProducts(_options.MaxPages);
                
                Console.WriteLine($"\n📋 APERÇU DES PRODUITS EXTRAITS ({products.Count} total)");
                Console.WriteLine("=" + new string('=', 60));
                
                for (int i = 0; i < Math.Min(products.Count, 20); i++) // Afficher les 20 premiers
                {
                    var product = products[i];
                    Console.WriteLine($"\n{i + 1}. {product.Title}");
                    Console.WriteLine($"   💰 Prix : {product.Price} {(string.IsNullOrEmpty(product.OriginalPrice) ? "" : $"(était {product.OriginalPrice})")}");
                    Console.WriteLine($"   🏷️  Réduction : {product.Discount}");
                    Console.WriteLine($"   ⭐ Note : {product.Rating} ({product.ReviewCount})");
                    Console.WriteLine($"   🔗 URL : {product.ProductUrl}");
                }
                
                if (products.Count > 20)
                {
                    Console.WriteLine($"\n... et {products.Count - 20} autres produits");
                }

                // Sauvegarder la liste complète
                if (_options.SaveProductsJson)
                {
                    _dealsExtractor.GenerateAffiliateLinks(_options.AmazonAssociateTag);
                    await _dealsExtractor.SaveProductsToJson("preview_products.json");
                }
            }
            finally
            {
                await _amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Charge des produits depuis un fichier JSON existant et génère les posts
        /// </summary>
        public async Task ProcessFromJson(string jsonFilePath)
        {
            try
            {
                if (!File.Exists(jsonFilePath))
                {
                    Console.WriteLine($"❌ Fichier JSON non trouvé : {jsonFilePath}");
                    return;
                }

                Console.WriteLine($"📂 Chargement des produits depuis : {jsonFilePath}");
                
                var json = await File.ReadAllTextAsync(jsonFilePath);
                var products = JsonSerializer.Deserialize<List<ProductInfo>>(json);

                if (products == null || products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit trouvé dans le fichier JSON");
                    return;
                }

                Console.WriteLine($"✅ {products.Count} produits chargés depuis le JSON");

                if (_options.GeneratePosts)
                {
                    // Pas besoin du navigateur pour cette méthode, on utilise directement l'API
                    var postGenerator = new FacebookPostGenerator(Environment.GetEnvironmentVariable("GEMINI_API_KEY") ?? "");
                    
                    if (!Directory.Exists(_options.OutputDirectory))
                    {
                        Directory.CreateDirectory(_options.OutputDirectory);
                    }

                    for (int i = 0; i < products.Count; i++)
                    {
                        var product = products[i];
                        try
                        {
                            Console.WriteLine($"📝 Génération du post {i + 1}/{products.Count} : {product.Title[..Math.Min(50, product.Title.Length)]}...");
                            
                            var post = await postGenerator.GeneratePostFromUrl(product.ProductUrl, product.AffiliateLink);
                            
                            var fileName = $"post_from_json_{i + 1:D3}.txt";
                            var filePath = Path.Combine(_options.OutputDirectory, fileName);
                            
                            await File.WriteAllTextAsync(filePath, post);
                            Console.WriteLine($"✅ Post sauvegardé : {fileName}");
                            
                            await Task.Delay(_options.DelayBetweenPosts);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ Erreur pour le produit {i + 1} : {ex.Message}");
                        }
                    }
                }

                Console.WriteLine("🎉 Traitement depuis JSON terminé !");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du traitement depuis JSON : {ex.Message}");
            }
        }
    }
}
