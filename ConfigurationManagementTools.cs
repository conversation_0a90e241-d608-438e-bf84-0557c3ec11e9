using PuppeteerSharp;
using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Tools for managing, testing, and updating extraction configurations
    /// </summary>
    public class ConfigurationManagementTools
    {
        private readonly ExtractionConfigurationManager _configManager;
        private readonly ConfigurableExtractionEngine _extractionEngine;

        public ConfigurationManagementTools(string configPath = "extraction-rules.json", string region = "amazon.fr")
        {
            _configManager = new ExtractionConfigurationManager(configPath);
            _extractionEngine = new ConfigurableExtractionEngine(_configManager, region);
        }

        /// <summary>
        /// Interactive configuration editor
        /// </summary>
        public async Task RunConfigurationEditorAsync()
        {
            Console.WriteLine("🔧 EXTRACTION CONFIGURATION EDITOR");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();

            while (true)
            {
                DisplayConfigurationMenu();
                var choice = Console.ReadLine()?.Trim();

                try
                {
                    var shouldExit = await HandleConfigurationMenuChoice(choice);
                    if (shouldExit) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error: {ex.Message}");
                    Console.WriteLine("Press any key to continue...");
                    Console.ReadKey();
                }
            }
        }

        /// <summary>
        /// Display configuration management menu
        /// </summary>
        private void DisplayConfigurationMenu()
        {
            Console.Clear();
            Console.WriteLine("🔧 Configuration Management Tools");
            Console.WriteLine("=" + new string('=', 35));
            Console.WriteLine();
            Console.WriteLine("📋 CONFIGURATION OPERATIONS:");
            Console.WriteLine("1. 📖 View Current Configuration");
            Console.WriteLine("2. ✅ Validate Configuration");
            Console.WriteLine("3. 🔄 Reload Configuration");
            Console.WriteLine("4. 📝 Create Default Configuration");
            Console.WriteLine();
            Console.WriteLine("🧪 TESTING TOOLS:");
            Console.WriteLine("5. 🎯 Test Selectors on Live Page");
            Console.WriteLine("6. 📊 Analyze Page Structure");
            Console.WriteLine("7. 🔍 Test Single Product Extraction");
            Console.WriteLine();
            Console.WriteLine("⚙️ UTILITIES:");
            Console.WriteLine("8. 📈 Performance Benchmark");
            Console.WriteLine("9. 🔧 Configuration Wizard");
            Console.WriteLine("10. ❌ Exit");
            Console.WriteLine();
            Console.Write("Choose an option (1-10): ");
        }

        /// <summary>
        /// Handle configuration menu choices
        /// </summary>
        private async Task<bool> HandleConfigurationMenuChoice(string? choice)
        {
            return choice switch
            {
                "1" => await ViewCurrentConfigurationAsync(),
                "2" => await ValidateConfigurationAsync(),
                "3" => await ReloadConfigurationAsync(),
                "4" => await CreateDefaultConfigurationAsync(),
                "5" => await TestSelectorsOnLivePageAsync(),
                "6" => await AnalyzePageStructureAsync(),
                "7" => await TestSingleProductExtractionAsync(),
                "8" => await RunPerformanceBenchmarkAsync(),
                "9" => await RunConfigurationWizardAsync(),
                "10" => true, // Exit
                _ => await HandleInvalidChoiceAsync()
            };
        }

        /// <summary>
        /// View current configuration
        /// </summary>
        private async Task<bool> ViewCurrentConfigurationAsync()
        {
            Console.Clear();
            Console.WriteLine("📖 CURRENT CONFIGURATION");
            Console.WriteLine("=" + new string('=', 25));

            try
            {
                var config = await _configManager.LoadConfigurationAsync();
                
                Console.WriteLine($"Version: {config.Version}");
                Console.WriteLine($"Last Updated: {config.LastUpdated:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"Description: {config.Description}");
                Console.WriteLine();

                Console.WriteLine("🌍 REGIONS:");
                foreach (var (key, region) in config.Regions)
                {
                    Console.WriteLine($"  • {key}: {region.Name} ({region.Currency})");
                }
                Console.WriteLine();

                Console.WriteLine("🎯 EXTRACTION RULES:");
                if (config.ExtractionRules.ProductList != null)
                {
                    Console.WriteLine($"  • Product List: {config.ExtractionRules.ProductList.Fields.Count} fields, {config.ExtractionRules.ProductList.ContainerSelectors.Selectors.Count} container selectors");
                }
                if (config.ExtractionRules.ProductPage != null)
                {
                    Console.WriteLine($"  • Product Page: {config.ExtractionRules.ProductPage.Fields.Count} fields, {config.ExtractionRules.ProductPage.ContainerSelectors.Selectors.Count} container selectors");
                }
                Console.WriteLine();

                Console.WriteLine("🔧 TRANSFORMATIONS:");
                foreach (var (name, transformation) in config.Transformations)
                {
                    Console.WriteLine($"  • {name}: {transformation.Description}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to load configuration: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Validate configuration
        /// </summary>
        private async Task<bool> ValidateConfigurationAsync()
        {
            Console.Clear();
            Console.WriteLine("✅ CONFIGURATION VALIDATION");
            Console.WriteLine("=" + new string('=', 30));

            try
            {
                var config = await _configManager.LoadConfigurationAsync();
                var result = await _configManager.ValidateConfigurationAsync(config);

                if (result.IsValid)
                {
                    Console.WriteLine("✅ Configuration is valid!");
                }
                else
                {
                    Console.WriteLine("❌ Configuration has errors:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"  • {error}");
                    }
                }

                if (result.Warnings.Any())
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠️ Warnings:");
                    foreach (var warning in result.Warnings)
                    {
                        Console.WriteLine($"  • {warning}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Validation failed: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Test selectors on live page
        /// </summary>
        private async Task<bool> TestSelectorsOnLivePageAsync()
        {
            Console.Clear();
            Console.WriteLine("🎯 LIVE SELECTOR TESTING");
            Console.WriteLine("=" + new string('=', 25));

            Console.Write("Enter page URL (or press Enter for Amazon deals): ");
            var url = Console.ReadLine()?.Trim();
            if (string.IsNullOrEmpty(url))
            {
                url = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
            }

            Console.WriteLine($"🌐 Testing selectors on: {url}");
            Console.WriteLine("⏳ Loading page...");

            try
            {
                using var browser = await Puppeteer.LaunchAsync(new LaunchOptions { Headless = false });
                using var page = await browser.NewPageAsync();
                
                await page.GoToAsync(url, new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });
                
                Console.WriteLine("✅ Page loaded. Testing selectors...");
                
                var result = await _extractionEngine.TestSelectorsAsync(page, "productList");
                
                Console.WriteLine();
                if (result.IsSuccessful)
                {
                    Console.WriteLine("✅ Selector test completed successfully!");
                }
                else
                {
                    Console.WriteLine("❌ Selector test found issues:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"  • {error}");
                    }
                }

                if (result.Warnings.Any())
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠️ Warnings:");
                    foreach (var warning in result.Warnings)
                    {
                        Console.WriteLine($"  • {warning}");
                    }
                }

                if (result.Info.Any())
                {
                    Console.WriteLine();
                    Console.WriteLine("ℹ️ Results:");
                    foreach (var info in result.Info)
                    {
                        Console.WriteLine($"  • {info}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Selector test failed: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Test single product extraction
        /// </summary>
        private async Task<bool> TestSingleProductExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🔍 SINGLE PRODUCT EXTRACTION TEST");
            Console.WriteLine("=" + new string('=', 35));

            Console.Write("Enter product URL: ");
            var url = Console.ReadLine()?.Trim();
            
            if (string.IsNullOrEmpty(url))
            {
                Console.WriteLine("❌ URL is required");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }

            try
            {
                using var browser = await Puppeteer.LaunchAsync(new LaunchOptions { Headless = false });
                using var page = await browser.NewPageAsync();
                
                await page.GoToAsync(url, new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });
                
                Console.WriteLine("✅ Page loaded. Extracting product...");
                
                var product = await _extractionEngine.ExtractSingleProductAsync(page, "body", "productPage");
                
                if (product != null)
                {
                    Console.WriteLine("✅ Product extracted successfully!");
                    Console.WriteLine();
                    Console.WriteLine("📦 EXTRACTED DATA:");
                    Console.WriteLine($"Title: {product.Title}");
                    Console.WriteLine($"Price: {product.Price}");
                    Console.WriteLine($"Discount: {product.Discount}");
                    Console.WriteLine($"Rating: {product.Rating}");
                    Console.WriteLine($"ASIN: {product.ASIN}");
                    Console.WriteLine($"Image: {product.ImageUrl}");
                    
                    // Validate extracted data
                    var validationResult = _extractionEngine.ValidateExtractedData(product, "productPage");
                    if (!validationResult.IsValid)
                    {
                        Console.WriteLine();
                        Console.WriteLine("⚠️ Validation Issues:");
                        foreach (var error in validationResult.Errors)
                        {
                            Console.WriteLine($"  • {error}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("❌ No product data extracted");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Extraction test failed: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Create default configuration
        /// </summary>
        private async Task<bool> CreateDefaultConfigurationAsync()
        {
            Console.Clear();
            Console.WriteLine("📝 CREATE DEFAULT CONFIGURATION");
            Console.WriteLine("=" + new string('=', 35));

            try
            {
                await _configManager.CreateDefaultConfigurationAsync();
                Console.WriteLine("✅ Default configuration created successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to create default configuration: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Reload configuration
        /// </summary>
        private async Task<bool> ReloadConfigurationAsync()
        {
            Console.Clear();
            Console.WriteLine("🔄 RELOAD CONFIGURATION");
            Console.WriteLine("=" + new string('=', 25));

            try
            {
                var config = await _configManager.ReloadConfigurationAsync();
                Console.WriteLine($"✅ Configuration reloaded successfully! Version: {config.Version}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to reload configuration: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Placeholder methods for additional tools
        /// </summary>
        private async Task<bool> AnalyzePageStructureAsync()
        {
            Console.WriteLine("🔧 Page structure analysis tool - Coming soon!");
            await Task.Delay(1000);
            return false;
        }

        private async Task<bool> RunPerformanceBenchmarkAsync()
        {
            Console.WriteLine("📈 Performance benchmark tool - Coming soon!");
            await Task.Delay(1000);
            return false;
        }

        private async Task<bool> RunConfigurationWizardAsync()
        {
            Console.WriteLine("🔧 Configuration wizard - Coming soon!");
            await Task.Delay(1000);
            return false;
        }

        private async Task<bool> HandleInvalidChoiceAsync()
        {
            Console.WriteLine("❌ Invalid option. Please choose 1-10.");
            await Task.Delay(1500);
            return false;
        }
    }
}
