{"$schema": "./extraction-rules-schema.json", "version": "1.0.0", "lastUpdated": "2024-12-22T10:00:00Z", "description": "Centralized extraction rules for Amazon product scraping", "regions": {"amazon.fr": {"name": "Amazon France", "baseUrl": "https://www.amazon.fr", "currency": "EUR", "language": "fr", "dealsUrl": "https://www.amazon.fr/deals?ref_=nav_cs_gb"}, "amazon.com": {"name": "Amazon US", "baseUrl": "https://www.amazon.com", "currency": "USD", "language": "en", "dealsUrl": "https://www.amazon.com/deals?ref_=nav_cs_gb"}}, "extractionRules": {"productList": {"description": "Rules for extracting products from deals/listing pages", "containerSelectors": {"priority": 1, "description": "Selectors to find product containers", "selectors": [{"selector": "div[data-testid='product-card'][data-asin]:not([data-asin=''])", "priority": 1, "description": "Primary product card with test ID and ASIN", "required": ["data-asin"]}, {"selector": "div[class*='ProductCard-module__card'][data-asin]:not([data-asin=''])", "priority": 2, "description": "Dynamic class product card with ASIN", "required": ["data-asin"]}, {"selector": "div[data-asin]:not([data-asin=''])", "priority": 3, "description": "Generic container with ASIN", "required": ["data-asin"]}, {"selector": "div[class*='s-result-item'][data-asin]:not([data-asin=''])", "priority": 4, "description": "Search result item with ASIN", "required": ["data-asin"]}]}, "fields": {"title": {"description": "Product title extraction", "selectors": [{"selector": ".a-truncate-full", "priority": 1, "description": "Full title in truncate container", "extraction": "textContent", "transform": "trim"}, {"selector": ".a-truncate .a-truncate-full", "priority": 2, "description": "Nested truncate full title", "extraction": "textContent", "transform": "trim"}, {"selector": "[class*='truncate'] [class*='full']", "priority": 3, "description": "Dynamic truncate classes", "extraction": "textContent", "transform": "trim"}, {"selector": "h3 a span, h2 a span", "priority": 4, "description": "Header link spans", "extraction": "textContent", "transform": "trim"}, {"selector": "[data-cy='title-recipe-title'] span", "priority": 5, "description": "Data attribute title", "extraction": "textContent", "transform": "trim"}], "validation": {"minLength": 10, "maxLength": 500, "required": true}}, "url": {"description": "Product URL extraction", "selectors": [{"selector": "a[data-testid='product-card-link']", "priority": 1, "description": "Primary product link", "extraction": "href", "transform": "normalizeUrl"}, {"selector": "h3 a, h2 a", "priority": 2, "description": "Header links", "extraction": "href", "transform": "normalizeUrl"}, {"selector": ".a-link-normal", "priority": 3, "description": "Normal Amazon link", "extraction": "href", "transform": "normalizeUrl"}], "validation": {"pattern": "^https://www\\.amazon\\.(fr|com)/.*", "required": true}, "fallback": {"generateFromASIN": true, "template": "{baseUrl}/dp/{asin}"}}, "asin": {"description": "Amazon Standard Identification Number", "selectors": [{"selector": "[data-asin]", "priority": 1, "description": "Direct ASIN from data attribute", "extraction": "data-asin", "transform": "trim"}], "validation": {"pattern": "^[A-Z0-9]{10}$", "required": true}, "fallback": {"extractFromUrl": true, "pattern": "/dp/([A-Z0-9]{10})"}}, "price": {"description": "Current product price", "selectors": [{"selector": ".a-price .a-offscreen", "priority": 1, "description": "Primary price element", "extraction": "textContent", "transform": "trim"}, {"selector": ".a-price-whole", "priority": 2, "description": "Whole price element", "extraction": "textContent", "transform": "trim"}, {"selector": "[class*='price'] .a-offscreen", "priority": 3, "description": "Dynamic price classes", "extraction": "textContent", "transform": "trim"}], "validation": {"pattern": "\\d+[,.]?\\d*\\s*€?", "required": false}}, "discount": {"description": "Discount information", "selectors": [{"selector": "div[class*='style_badgeContainer'] span", "priority": 1, "description": "Amazon badge container", "extraction": "textContent", "transform": "extractDiscount"}, {"selector": "[class*='badge'][class*='Container'] span", "priority": 2, "description": "Dynamic badge container", "extraction": "textContent", "transform": "extractDiscount"}, {"selector": "[class*='discount'] span", "priority": 3, "description": "Discount specific elements", "extraction": "textContent", "transform": "extractDiscount"}], "validation": {"pattern": "^-?\\d+(%|€)$", "required": false}}, "image": {"description": "Product image URL", "selectors": [{"selector": "img[src*='images-amazon']", "priority": 1, "description": "Amazon CDN images", "extraction": "src", "transform": "trim"}, {"selector": "img[data-src*='images-amazon']", "priority": 2, "description": "Lazy loaded Amazon images", "extraction": "data-src", "transform": "trim"}, {"selector": "img", "priority": 3, "description": "Any image in container", "extraction": "src", "transform": "trim"}], "validation": {"pattern": "^https://.*\\.(jpg|jpeg|png|webp)", "required": false}}, "dealId": {"description": "Deal identifier", "selectors": [{"selector": "[data-deal-id]", "priority": 1, "description": "Deal ID from data attribute", "extraction": "data-deal-id", "transform": "trim"}], "validation": {"required": false}}, "rating": {"description": "Product rating", "selectors": [{"selector": "[class*='rating'] span", "priority": 1, "description": "Rating span elements", "extraction": "textContent", "transform": "extractRating"}, {"selector": ".a-icon-alt", "priority": 2, "description": "Icon alt text", "extraction": "textContent", "transform": "extractRating"}], "validation": {"pattern": "^[0-5]([,.]\\d)?$", "required": false}}}}, "productPage": {"description": "Rules for extracting detailed information from individual product pages", "containerSelectors": {"priority": 1, "description": "Selectors to find the main product container", "selectors": [{"selector": "#dp-container", "priority": 1, "description": "Main product detail container"}, {"selector": "[data-asin]", "priority": 2, "description": "Container with ASIN attribute"}, {"selector": "body", "priority": 3, "description": "Fallback to body element"}]}, "fields": {"title": {"description": "Detailed product title", "selectors": [{"selector": "#productTitle", "priority": 1, "description": "Main product title", "extraction": "textContent", "transform": "trim"}, {"selector": "h1 span", "priority": 2, "description": "H1 title span", "extraction": "textContent", "transform": "trim"}], "validation": {"minLength": 10, "maxLength": 500, "required": true}}, "price": {"description": "Current product price", "selectors": [{"selector": ".a-price.a-text-price .a-offscreen", "priority": 1, "description": "Primary price element", "extraction": "textContent", "transform": "trim"}, {"selector": "#priceblock_dealprice", "priority": 2, "description": "Deal price element", "extraction": "textContent", "transform": "trim"}, {"selector": "#priceblock_ourprice", "priority": 3, "description": "Our price element", "extraction": "textContent", "transform": "trim"}], "validation": {"pattern": "\\d+[,.]?\\d*\\s*€?", "required": false}}, "originalPrice": {"description": "Original price before discount", "selectors": [{"selector": ".a-price.a-text-price.a-size-base .a-offscreen", "priority": 1, "description": "Original price element", "extraction": "textContent", "transform": "trim"}, {"selector": "#listPrice .a-offscreen", "priority": 2, "description": "List price element", "extraction": "textContent", "transform": "trim"}], "validation": {"pattern": "\\d+[,.]?\\d*\\s*€?", "required": false}}, "rating": {"description": "Product rating", "selectors": [{"selector": "#acrPopover .a-icon-alt", "priority": 1, "description": "Rating icon alt text", "extraction": "textContent", "transform": "extractRating"}, {"selector": "[data-hook='rating-out-of-text']", "priority": 2, "description": "Rating text element", "extraction": "textContent", "transform": "extractRating"}], "validation": {"pattern": "^[0-5]([,.]\\d)?$", "required": false}}, "reviewCount": {"description": "Number of reviews", "selectors": [{"selector": "#acrCustomerReviewText", "priority": 1, "description": "Review count text", "extraction": "textContent", "transform": "trim"}, {"selector": "[data-hook='total-review-count']", "priority": 2, "description": "Total review count", "extraction": "textContent", "transform": "trim"}], "validation": {"pattern": "\\d+", "required": false}}, "affiliateLink": {"description": "Amazon affiliate link", "selectors": [{"selector": "a:has-text('Obtenir un lien')", "priority": 1, "description": "Get link button", "extraction": "href", "transform": "normalizeUrl"}, {"selector": "[data-testid='affiliate-link']", "priority": 2, "description": "Affiliate link element", "extraction": "href", "transform": "normalizeUrl"}], "validation": {"pattern": "^https://.*amazon.*", "required": false}}, "commissionRate": {"description": "Commission rate for affiliate", "selectors": [{"selector": "[class*='commission'] span", "priority": 1, "description": "Commission rate span", "extraction": "textContent", "transform": "trim"}, {"selector": ".affiliate-rate", "priority": 2, "description": "Affiliate rate element", "extraction": "textContent", "transform": "trim"}], "validation": {"pattern": "\\d+%", "required": false}}, "category": {"description": "Product category", "selectors": [{"selector": "#wayfinding-breadcrumbs_feature_div a", "priority": 1, "description": "Breadcrumb category links", "extraction": "textContent", "transform": "trim"}, {"selector": "[data-hook='breadcrumb'] a", "priority": 2, "description": "Breadcrumb links", "extraction": "textContent", "transform": "trim"}], "validation": {"required": false}}}}}, "transformations": {"trim": {"description": "Remove leading/trailing whitespace and normalize spaces", "function": "text => text.trim().replace(/\\s+/g, ' ')"}, "normalizeUrl": {"description": "Ensure URL is complete and properly formatted", "function": "url => url.startsWith('http') ? url : (url.startsWith('/') ? baseUrl + url : baseUrl + '/' + url)"}, "extractDiscount": {"description": "Extract discount percentage or amount", "function": "text => { const match = text.match(/(\\d+)\\s*(%|€)/); return match ? `-${match[1]}${match[2]}` : text; }"}, "extractRating": {"description": "Extract numeric rating from text", "function": "text => { const match = text.match(/(\\d+[,.]?\\d*)\\s*(?:sur|out of|\\/)/i); return match ? match[1].replace(',', '.') : ''; }"}}, "validation": {"globalRules": {"maxExtractionTime": 30000, "minProductsPerPage": 1, "maxProductsPerPage": 100}, "fieldRules": {"title": {"minLength": 10, "maxLength": 500}, "asin": {"pattern": "^[A-Z0-9]{10}$"}, "url": {"mustContain": ["/dp/", "amazon."]}}}}