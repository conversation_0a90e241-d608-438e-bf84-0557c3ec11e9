using System.Text.Json;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Manages loading, validation, and hot-reloading of extraction configuration
    /// </summary>
    public class ExtractionConfigurationManager : IDisposable
    {
        private readonly string _configurationPath;
        private readonly FileSystemWatcher _fileWatcher;
        private ExtractionConfiguration? _currentConfiguration;
        private readonly object _configLock = new();
        private bool _disposed = false;

        public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

        public ExtractionConfigurationManager(string configurationPath = "extraction-rules.json")
        {
            _configurationPath = Path.GetFullPath(configurationPath);
            
            // Set up file watcher for hot-reload
            var directory = Path.GetDirectoryName(_configurationPath)!;
            var fileName = Path.GetFileName(_configurationPath);
            
            _fileWatcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = true
            };
            
            _fileWatcher.Changed += OnConfigurationFileChanged;
        }

        /// <summary>
        /// Load configuration from file with validation
        /// </summary>
        public async Task<ExtractionConfiguration> LoadConfigurationAsync()
        {
            lock (_configLock)
            {
                if (_currentConfiguration != null)
                    return _currentConfiguration;
            }

            return await ReloadConfigurationAsync();
        }

        /// <summary>
        /// Reload configuration from file
        /// </summary>
        public async Task<ExtractionConfiguration> ReloadConfigurationAsync()
        {
            try
            {
                Logger.LogInfo($"Loading extraction configuration from: {_configurationPath}", "Configuration");

                if (!File.Exists(_configurationPath))
                {
                    throw new FileNotFoundException($"Configuration file not found: {_configurationPath}");
                }

                var json = await File.ReadAllTextAsync(_configurationPath);
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };

                var configuration = JsonSerializer.Deserialize<ExtractionConfiguration>(json, options);
                if (configuration == null)
                {
                    throw new InvalidOperationException("Failed to deserialize configuration");
                }

                // Validate configuration
                var validationResult = await ValidateConfigurationAsync(configuration);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException($"Configuration validation failed: {string.Join(", ", validationResult.Errors)}");
                }

                lock (_configLock)
                {
                    _currentConfiguration = configuration;
                }

                Logger.LogInfo($"Configuration loaded successfully. Version: {configuration.Version}", "Configuration");
                return configuration;
            }
            catch (Exception ex)
            {
                Logger.LogError($"Failed to load configuration: {ex.Message}", ex, "Configuration");
                throw;
            }
        }

        /// <summary>
        /// Get current configuration (thread-safe)
        /// </summary>
        public ExtractionConfiguration? GetCurrentConfiguration()
        {
            lock (_configLock)
            {
                return _currentConfiguration;
            }
        }

        /// <summary>
        /// Validate configuration structure and rules
        /// </summary>
        public async Task<ConfigurationValidationResult> ValidateConfigurationAsync(ExtractionConfiguration configuration)
        {
            var result = new ConfigurationValidationResult();

            try
            {
                // Basic structure validation
                if (string.IsNullOrEmpty(configuration.Version))
                {
                    result.AddError("Version is required");
                }
                else if (!Regex.IsMatch(configuration.Version, @"^\d+\.\d+\.\d+$"))
                {
                    result.AddError("Version must be in semver format (x.y.z)");
                }

                if (configuration.Regions == null || !configuration.Regions.Any())
                {
                    result.AddError("At least one region must be configured");
                }

                if (configuration.ExtractionRules == null)
                {
                    result.AddError("Extraction rules are required");
                }

                // Validate regions
                if (configuration.Regions != null)
                {
                    foreach (var (regionKey, region) in configuration.Regions)
                    {
                        if (!regionKey.StartsWith("amazon."))
                        {
                            result.AddError($"Region key '{regionKey}' must start with 'amazon.'");
                        }

                        if (string.IsNullOrEmpty(region.BaseUrl) || !Uri.TryCreate(region.BaseUrl, UriKind.Absolute, out _))
                        {
                            result.AddError($"Region '{regionKey}' has invalid base URL");
                        }

                        if (string.IsNullOrEmpty(region.Currency) || region.Currency.Length != 3)
                        {
                            result.AddError($"Region '{regionKey}' has invalid currency code");
                        }
                    }
                }

                // Validate extraction rules
                if (configuration.ExtractionRules != null)
                {
                    if (configuration.ExtractionRules.ProductList != null)
                    {
                        ValidateExtractionRuleSet(configuration.ExtractionRules.ProductList, "ProductList", result);
                    }

                    if (configuration.ExtractionRules.ProductPage != null)
                    {
                        ValidateExtractionRuleSet(configuration.ExtractionRules.ProductPage, "ProductPage", result);
                    }
                }

                // Validate transformations
                if (configuration.Transformations != null)
                {
                    foreach (var (name, transformation) in configuration.Transformations)
                    {
                        if (string.IsNullOrEmpty(transformation.Function))
                        {
                            result.AddError($"Transformation '{name}' has empty function");
                        }
                    }
                }

                Logger.LogInfo($"Configuration validation completed. Errors: {result.Errors.Count}, Warnings: {result.Warnings.Count}", "Configuration");
            }
            catch (Exception ex)
            {
                result.AddError($"Validation exception: {ex.Message}");
                Logger.LogError($"Configuration validation failed: {ex.Message}", ex, "Configuration");
            }

            return result;
        }

        /// <summary>
        /// Validate extraction rule set
        /// </summary>
        private void ValidateExtractionRuleSet(ExtractionRuleSet ruleSet, string ruleSetName, ConfigurationValidationResult result)
        {
            if (ruleSet.ContainerSelectors?.Selectors == null || !ruleSet.ContainerSelectors.Selectors.Any())
            {
                result.AddError($"{ruleSetName}: Container selectors are required");
            }
            else
            {
                foreach (var selector in ruleSet.ContainerSelectors.Selectors)
                {
                    if (string.IsNullOrEmpty(selector.Selector))
                    {
                        result.AddError($"{ruleSetName}: Empty selector found");
                    }

                    if (selector.Priority < 1)
                    {
                        result.AddError($"{ruleSetName}: Selector priority must be >= 1");
                    }
                }
            }

            if (ruleSet.Fields == null || !ruleSet.Fields.Any())
            {
                result.AddWarning($"{ruleSetName}: No field extraction rules defined");
            }
            else
            {
                foreach (var (fieldName, field) in ruleSet.Fields)
                {
                    if (field.Selectors == null || !field.Selectors.Any())
                    {
                        result.AddError($"{ruleSetName}.{fieldName}: No selectors defined");
                    }
                    else
                    {
                        foreach (var selector in field.Selectors)
                        {
                            if (string.IsNullOrEmpty(selector.Selector))
                            {
                                result.AddError($"{ruleSetName}.{fieldName}: Empty selector found");
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Test selectors against a sample page
        /// </summary>
        public async Task<SelectorTestResult> TestSelectorsAsync(string region, string pageType, string htmlContent)
        {
            var config = await LoadConfigurationAsync();
            var result = new SelectorTestResult();

            try
            {
                if (!config.Regions.ContainsKey(region))
                {
                    result.AddError($"Region '{region}' not found in configuration");
                    return result;
                }

                ExtractionRuleSet? ruleSet = pageType.ToLower() switch
                {
                    "productlist" => config.ExtractionRules.ProductList,
                    "productpage" => config.ExtractionRules.ProductPage,
                    _ => null
                };

                if (ruleSet == null)
                {
                    result.AddError($"Rule set '{pageType}' not found in configuration");
                    return result;
                }

                // This would require HTML parsing - simplified for now
                result.AddInfo($"Testing selectors for {region}/{pageType}");
                result.AddInfo($"Container selectors: {ruleSet.ContainerSelectors.Selectors.Count}");
                result.AddInfo($"Field extractors: {ruleSet.Fields.Count}");

                Logger.LogInfo($"Selector test completed for {region}/{pageType}", "Configuration");
            }
            catch (Exception ex)
            {
                result.AddError($"Selector test failed: {ex.Message}");
                Logger.LogError($"Selector test failed: {ex.Message}", ex, "Configuration");
            }

            return result;
        }

        /// <summary>
        /// Handle configuration file changes for hot-reload
        /// </summary>
        private async void OnConfigurationFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // Debounce file changes
                await Task.Delay(500);

                Logger.LogInfo("Configuration file changed, reloading...", "Configuration");
                var newConfiguration = await ReloadConfigurationAsync();

                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(newConfiguration));
            }
            catch (Exception ex)
            {
                Logger.LogError($"Failed to reload configuration after file change: {ex.Message}", ex, "Configuration");
            }
        }

        /// <summary>
        /// Create default configuration file if it doesn't exist
        /// </summary>
        public async Task CreateDefaultConfigurationAsync()
        {
            if (File.Exists(_configurationPath))
            {
                Logger.LogWarning($"Configuration file already exists: {_configurationPath}", "Configuration");
                return;
            }

            try
            {
                var defaultConfig = CreateDefaultConfiguration();
                var json = JsonSerializer.Serialize(defaultConfig, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(_configurationPath, json);
                Logger.LogInfo($"Default configuration created: {_configurationPath}", "Configuration");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Failed to create default configuration: {ex.Message}", ex, "Configuration");
                throw;
            }
        }

        /// <summary>
        /// Create a default configuration object
        /// </summary>
        private ExtractionConfiguration CreateDefaultConfiguration()
        {
            // This would return a basic configuration
            // For brevity, returning a minimal structure
            return new ExtractionConfiguration
            {
                Version = "1.0.0",
                LastUpdated = DateTime.UtcNow,
                Description = "Default extraction configuration",
                Regions = new Dictionary<string, RegionConfiguration>
                {
                    ["amazon.fr"] = new RegionConfiguration
                    {
                        Name = "Amazon France",
                        BaseUrl = "https://www.amazon.fr",
                        Currency = "EUR",
                        Language = "fr",
                        DealsUrl = "https://www.amazon.fr/deals?ref_=nav_cs_gb"
                    }
                },
                ExtractionRules = new ExtractionRules(),
                Transformations = new Dictionary<string, TransformationRule>(),
                Validation = new ValidationRules()
            };
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _fileWatcher?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Configuration data models
    /// </summary>
    public class ExtractionConfiguration
    {
        [JsonPropertyName("version")]
        public string Version { get; set; } = "";

        [JsonPropertyName("lastUpdated")]
        public DateTime LastUpdated { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("regions")]
        public Dictionary<string, RegionConfiguration> Regions { get; set; } = new();

        [JsonPropertyName("extractionRules")]
        public ExtractionRules ExtractionRules { get; set; } = new();

        [JsonPropertyName("transformations")]
        public Dictionary<string, TransformationRule> Transformations { get; set; } = new();

        [JsonPropertyName("validation")]
        public ValidationRules Validation { get; set; } = new();
    }

    public class RegionConfiguration
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = "";

        [JsonPropertyName("baseUrl")]
        public string BaseUrl { get; set; } = "";

        [JsonPropertyName("currency")]
        public string Currency { get; set; } = "";

        [JsonPropertyName("language")]
        public string Language { get; set; } = "";

        [JsonPropertyName("dealsUrl")]
        public string DealsUrl { get; set; } = "";
    }

    public class ExtractionRules
    {
        [JsonPropertyName("productList")]
        public ExtractionRuleSet? ProductList { get; set; }

        [JsonPropertyName("productPage")]
        public ExtractionRuleSet? ProductPage { get; set; }
    }

    public class ExtractionRuleSet
    {
        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("containerSelectors")]
        public ContainerSelectors ContainerSelectors { get; set; } = new();

        [JsonPropertyName("fields")]
        public Dictionary<string, FieldExtraction> Fields { get; set; } = new();
    }

    public class ContainerSelectors
    {
        [JsonPropertyName("priority")]
        public int Priority { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("selectors")]
        public List<SelectorRule> Selectors { get; set; } = new();
    }

    public class SelectorRule
    {
        [JsonPropertyName("selector")]
        public string Selector { get; set; } = "";

        [JsonPropertyName("priority")]
        public int Priority { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("required")]
        public List<string> Required { get; set; } = new();

        [JsonPropertyName("extraction")]
        public string Extraction { get; set; } = "textContent";

        [JsonPropertyName("transform")]
        public string Transform { get; set; } = "";
    }

    public class FieldExtraction
    {
        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("selectors")]
        public List<SelectorRule> Selectors { get; set; } = new();

        [JsonPropertyName("validation")]
        public FieldValidation? Validation { get; set; }

        [JsonPropertyName("fallback")]
        public FallbackRule? Fallback { get; set; }
    }

    public class FieldValidation
    {
        [JsonPropertyName("required")]
        public bool Required { get; set; }

        [JsonPropertyName("minLength")]
        public int? MinLength { get; set; }

        [JsonPropertyName("maxLength")]
        public int? MaxLength { get; set; }

        [JsonPropertyName("pattern")]
        public string? Pattern { get; set; }

        [JsonPropertyName("mustContain")]
        public List<string> MustContain { get; set; } = new();
    }

    public class FallbackRule
    {
        [JsonPropertyName("generateFromASIN")]
        public bool GenerateFromASIN { get; set; }

        [JsonPropertyName("template")]
        public string Template { get; set; } = "";

        [JsonPropertyName("extractFromUrl")]
        public bool ExtractFromUrl { get; set; }

        [JsonPropertyName("pattern")]
        public string Pattern { get; set; } = "";
    }

    public class TransformationRule
    {
        [JsonPropertyName("description")]
        public string Description { get; set; } = "";

        [JsonPropertyName("function")]
        public string Function { get; set; } = "";
    }

    public class ValidationRules
    {
        [JsonPropertyName("globalRules")]
        public GlobalValidationRules GlobalRules { get; set; } = new();

        [JsonPropertyName("fieldRules")]
        public Dictionary<string, FieldValidation> FieldRules { get; set; } = new();
    }

    public class GlobalValidationRules
    {
        [JsonPropertyName("maxExtractionTime")]
        public int MaxExtractionTime { get; set; } = 30000;

        [JsonPropertyName("minProductsPerPage")]
        public int MinProductsPerPage { get; set; } = 1;

        [JsonPropertyName("maxProductsPerPage")]
        public int MaxProductsPerPage { get; set; } = 100;
    }

    /// <summary>
    /// Validation and test result classes
    /// </summary>
    public class ConfigurationValidationResult
    {
        public List<string> Errors { get; } = new();
        public List<string> Warnings { get; } = new();
        public bool IsValid => !Errors.Any();

        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
    }

    public class SelectorTestResult
    {
        public List<string> Errors { get; } = new();
        public List<string> Warnings { get; } = new();
        public List<string> Info { get; } = new();
        public bool IsSuccessful => !Errors.Any();

        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
        public void AddInfo(string info) => Info.Add(info);
    }

    public class ConfigurationChangedEventArgs : EventArgs
    {
        public ExtractionConfiguration NewConfiguration { get; }

        public ConfigurationChangedEventArgs(ExtractionConfiguration newConfiguration)
        {
            NewConfiguration = newConfiguration;
        }
    }
}
