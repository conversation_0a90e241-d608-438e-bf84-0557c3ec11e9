using PuppeteerSharp;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Optimized product information model with validation and improved serialization
    /// </summary>
    public class ProductInfo
    {
        [Required]
        [JsonPropertyName("title")]
        public string Title { get; set; } = "";

        [Required]
        [Url]
        [JsonPropertyName("productUrl")]
        public string ProductUrl { get; set; } = "";

        [Url]
        [JsonPropertyName("imageUrl")]
        public string ImageUrl { get; set; } = "";

        [JsonPropertyName("price")]
        public string Price { get; set; } = "";

        [JsonPropertyName("originalPrice")]
        public string OriginalPrice { get; set; } = "";

        [JsonPropertyName("discount")]
        public string Discount { get; set; } = "";

        [JsonPropertyName("rating")]
        public string Rating { get; set; } = "";

        [JsonPropertyName("reviewCount")]
        public string ReviewCount { get; set; } = "";

        [JsonPropertyName("category")]
        public string Category { get; set; } = "";

        [Url]
        [JsonPropertyName("affiliateLink")]
        public string AffiliateLink { get; set; } = "";

        [JsonPropertyName("commissionRate")]
        public string CommissionRate { get; set; } = "";

        [JsonPropertyName("facebookPost")]
        public string FacebookPost { get; set; } = "";

        [JsonPropertyName("isDeal")]
        public bool IsDeal { get; set; } = false;

        [JsonPropertyName("extractedAt")]
        public DateTime ExtractedAt { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("asin")]
        public string ASIN => ExtractASIN(ProductUrl);

        /// <summary>
        /// Validates the product information
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Title) &&
                   !string.IsNullOrWhiteSpace(ProductUrl) &&
                   Uri.TryCreate(ProductUrl, UriKind.Absolute, out _);
        }

        /// <summary>
        /// Extracts ASIN from product URL
        /// </summary>
        private static string ExtractASIN(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";
            var match = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            return match.Success ? match.Groups[1].Value : "";
        }

        /// <summary>
        /// Creates a summary for logging/display purposes
        /// </summary>
        public string GetSummary()
        {
            var titlePreview = Title.Length > 50 ? Title[..47] + "..." : Title;
            return $"{titlePreview} | {Price} | {Discount}";
        }
    }

    internal class AmazonDealsExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private FacebookPostGenerator? _postGenerator;
        private readonly List<ProductInfo> _extractedProducts;

        public AmazonDealsExtractor(AmazonLoader<string> amazonLoader)
        {
            _amazonLoader = amazonLoader;
            _extractedProducts = new List<ProductInfo>();
        }

        public void SetGeminiApiKey(string apiKey)
        {
            _postGenerator = new FacebookPostGenerator(apiKey);
        }

        /// <summary>
        /// Extrait tous les produits de la page des offres Amazon et des pages suivantes
        /// </summary>
        /// <param name="maxPages">Nombre maximum de pages à parcourir (0 = toutes les pages)</param>
        /// <returns>Liste des produits extraits</returns>
        public async Task<List<ProductInfo>> ExtractAllDealsProducts(int maxPages = 5)
        {
            var dealsUrl = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
            _extractedProducts.Clear();

            Console.WriteLine("🔍 Début de l'extraction des produits en promotion...");
            
            await _amazonLoader.LoadPage(dealsUrl, async (page) =>
            {
                int currentPage = 1;
                bool hasNextPage = true;

                while (hasNextPage && (maxPages == 0 || currentPage <= maxPages))
                {
                    Console.WriteLine($"📄 Extraction de la page {currentPage}...");

                    // Attendre que la page soit complètement chargée et que le JavaScript s'exécute
                    await Task.Delay(3000);

                    // Attendre que les éléments de produits soient présents avec plusieurs tentatives
                    bool elementsFound = false;
                    var selectors = new[]
                    {
                        "a[href*='/dp/']",
                        "[data-asin]",
                        ".a-carousel-card",
                        "[data-csa-c-type='widget']",
                        ".s-result-item"
                    };

                    for (int attempt = 0; attempt < 3 && !elementsFound; attempt++)
                    {
                        Console.WriteLine($"🔍 Tentative {attempt + 1}/3 de détection des produits...");

                        foreach (var selector in selectors)
                        {
                            try
                            {
                                await page.WaitForSelectorAsync(selector, new WaitForSelectorOptions
                                {
                                    Timeout = 5000
                                });
                                Console.WriteLine($"✅ Éléments détectés avec le sélecteur: {selector}");
                                elementsFound = true;
                                break;
                            }
                            catch
                            {
                                // Continuer avec le sélecteur suivant
                            }
                        }

                        if (!elementsFound)
                        {
                            Console.WriteLine($"⚠️ Tentative {attempt + 1} échouée, attente supplémentaire...");
                            await Task.Delay(3000);
                        }
                    }

                    if (!elementsFound)
                    {
                        Console.WriteLine("⚠️ Aucun élément de produit détecté après 3 tentatives");
                        Console.WriteLine("Tentative d'extraction quand même...");
                    }

                    // Extraire les produits de la page actuelle
                    var pageProducts = await ExtractProductsFromCurrentPage(page);
                    _extractedProducts.AddRange(pageProducts);

                    Console.WriteLine($"✅ {pageProducts.Count} produits extraits de la page {currentPage}");
                    Console.WriteLine($"📊 Total produits extraits : {_extractedProducts.Count}");

                    // Si aucun produit n'est trouvé, essayer de diagnostiquer
                    if (pageProducts.Count == 0)
                    {
                        Console.WriteLine("🔍 Aucun produit trouvé, diagnostic en cours...");
                        await DiagnosePageContent(page);
                    }

                    // Vérifier s'il y a une page suivante et naviguer
                    hasNextPage = await NavigateToNextPage(page);

                    if (hasNextPage)
                    {
                        currentPage++;
                        // Attendre entre les pages pour éviter d'être bloqué
                        await Task.Delay(3000);
                    }
                }

                Console.WriteLine($"🎯 Extraction terminée ! Total : {_extractedProducts.Count} produits");
                return "";
            });

            return _extractedProducts;
        }

        /// <summary>
        /// Extrait les produits de la page actuelle avec scraping direct optimisé
        /// </summary>
        private async Task<List<ProductInfo>> ExtractProductsFromCurrentPage(IPage page)
        {
            var products = await page.EvaluateFunctionAsync<ProductInfo[]>(@"() => {
                const products = [];

                // Sélecteurs directs basés sur la structure Amazon actuelle
                // Priorité aux sélecteurs spécifiques identifiés par l'utilisateur
                const productSelectors = [
                    // Sélecteurs directs pour les cartes de produits (PRIORITÉ)
                    'div[class*=ProductCard-module__card][data-asin]',
                    'div[class^=ProductCard-module__card][data-asin]',
                    // Fallback pour les conteneurs avec data-asin
                    '[data-asin]:not([data-asin=])',
                    // Anciens sélecteurs en fallback
                    '[data-csa-c-type=widget] [data-asin]',
                    '[data-csa-c-slot-id] [data-asin]',
                    '.a-carousel-card',
                    '.p13n-desktop-carousel-cards [data-asin]',
                    'a[href*=/dp/]',
                    'a[href*=/gp/product/]',
                    '.a-section[data-asin]',
                    '[data-component-type=s-search-result]',
                    '.s-result-item',
                    '[data-deal-id]',
                    '.DealCard',
                    '.deal-card',
                    '.octopus-dlp-asin-section'
                ];

                let productElements = [];
                let usedSelector = '';

                // Essayer différents sélecteurs et garder le premier qui fonctionne
                for (const selector of productSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        productElements = Array.from(elements);
                        usedSelector = selector;
                        console.log('Sélecteur utilisé: ' + selector + ' (' + elements.length + ' éléments)');
                        break;
                    }
                }

                // Si aucun sélecteur spécifique ne fonctionne, essayer une approche plus générale
                if (productElements.length === 0) {
                    console.log('Aucun conteneur trouvé, essai avec les liens directs...');

                    // Essayer de trouver des éléments avec des liens vers des produits
                    const linkElements = document.querySelectorAll('a[href*=""/dp/""]');
                    console.log(linkElements.length + ' liens produits trouvés');

                    if (linkElements.length > 0) {
                        // Remonter au conteneur parent de chaque lien
                        const containers = new Set();
                        linkElements.forEach(link => {
                            // Essayer différents niveaux de parents
                            let parent = link.closest('[data-asin], .a-section, div[data-testid], div[class*=deal], div[class*=product]');

                            if (!parent) {
                                // Remonter de 3 niveaux maximum pour trouver un conteneur approprié
                                let current = link.parentElement;
                                for (let i = 0; i < 3 && current; i++) {
                                    if (current.tagName === 'DIV' && (
                                        current.children.length > 1 ||
                                        current.querySelector('img') ||
                                        current.textContent.includes('€') ||
                                        current.textContent.includes('%')
                                    )) {
                                        parent = current;
                                        break;
                                    }
                                    current = current.parentElement;
                                }
                            }

                            if (!parent) {
                                // En dernier recours, prendre le parent div le plus proche
                                parent = link.closest('div');
                            }

                            if (parent && parent !== document.body) {
                                containers.add(parent);
                            }
                        });

                        productElements = Array.from(containers);
                        usedSelector = 'fallback: conteneurs de liens produits';
                        console.log('Fallback utilisé: ' + productElements.length + ' conteneurs trouvés');
                    }
                }

                productElements.forEach((element, index) => {
                    try {
                        const product = {
                            Title: '',
                            ProductUrl: '',
                            ImageUrl: '',
                            Price: '',
                            OriginalPrice: '',
                            Discount: '',
                            Rating: '',
                            ReviewCount: '',
                            Category: '',
                            AffiliateLink: '',
                            IsDeal: true
                        };

                        // Debug: afficher la structure de l'élément
                        if (index < 3) {
                            console.log('Debug élément ' + (index + 1) + ':');
                            console.log('  Tag: ' + element.tagName);
                            console.log('  Classes: ' + element.className);
                            console.log('  Data-ASIN: ' + (element.getAttribute('data-asin') || 'Non trouvé'));
                            console.log('  Texte (50 premiers chars): ' + element.textContent.trim().substring(0, 50) + '...');
                        }

                        // Titre du produit - sélecteurs mis à jour avec priorité aux nouveaux sélecteurs
                        const titleSelectors = [
                            // PRIORITÉ: Nouveaux sélecteurs identifiés
                            'a[data-testid=product-card-link]',
                            'a[data-testid=product-card-link] span',
                            // Sélecteurs spécifiques pour les liens produits
                            'a[href*=/dp/]',
                            'a[href*=/gp/product/]',
                            // Sélecteurs de texte dans les liens
                            'a[href*=/dp/] span',
                            'a[href*=/gp/product/] span',
                            // Sélecteurs Amazon 2025
                            'span[data-a-color=base]',
                            '.a-truncate-full',
                            '.a-truncate-cut',
                            // Sélecteurs de titres
                            'h3 a',
                            'h2 a',
                            'h3 span',
                            'h2 span',
                            'h3',
                            'h2',
                            // Sélecteurs génériques
                            '.a-size-base-plus',
                            '.a-size-mini span',
                            '.s-size-mini span',
                            '[data-cy=title]',
                            '.a-link-normal span',
                            '.a-text-normal',
                            // Attributs alt des images
                            'img[alt]'
                        ];

                        for (const selector of titleSelectors) {
                            const titleEl = element.querySelector(selector);
                            if (titleEl) {
                                let title = '';

                                // Si c'est un lien, prendre le texte ou l'attribut title
                                if (titleEl.tagName === 'A') {
                                    title = titleEl.textContent.trim() || titleEl.getAttribute('title') || titleEl.getAttribute('aria-label') || '';
                                }
                                // Si c'est une image, prendre l'alt
                                else if (titleEl.tagName === 'IMG') {
                                    title = titleEl.getAttribute('alt') || '';
                                }
                                // Sinon, prendre le texte
                                else {
                                    title = titleEl.textContent.trim();
                                }

                                // Vérifier que le titre est valide (plus strict)
                                if (title && title.length > 10 && title.length < 200 &&
                                    !title.includes('€') && !title.includes('%') &&
                                    !title.includes('★') && !title.includes('Voir plus')) {
                                    product.Title = title;
                                    break;
                                }
                            }
                        }

                        // Si pas de titre trouvé, essayer une approche plus ciblée
                        if (!product.Title) {
                            // Chercher spécifiquement dans les liens produits
                            const productLinks = element.querySelectorAll('a[href*=/dp/], a[data-testid=product-card-link]');
                            for (const link of productLinks) {
                                const linkText = link.textContent.trim();
                                if (linkText && linkText.length > 10 && linkText.length < 200 &&
                                    !linkText.includes('€') && !linkText.includes('%')) {
                                    product.Title = linkText;
                                    break;
                                }
                            }
                        }

                        // 5. Vérification finale de l'URL du produit (déjà extraite plus haut)
                        // Si pas encore d'URL, essayer les sélecteurs de fallback
                        if (!product.ProductUrl) {
                            const linkSelectors = [
                                // PRIORITÉ: Nouveau sélecteur identifié
                                'a[data-testid=product-card-link]',
                                'a[href*=/dp/]',
                                'a[href*=/gp/product/]',
                                'h3 a',
                                'h2 a',
                                '.a-link-normal'
                            ];

                            for (const selector of linkSelectors) {
                                const linkEl = element.querySelector(selector);
                                if (linkEl && linkEl.href && linkEl.href.includes('amazon')) {
                                    let url = linkEl.href;
                                    if (url.includes('/dp/')) {
                                        const asinMatch = url.match(/\/dp\/([A-Z0-9]{10})/);
                                        if (asinMatch) {
                                            product.ProductUrl = `https://www.amazon.fr/dp/${asinMatch[1]}`;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        // 6. Image du produit
                        const imgEl = element.querySelector('img');
                        if (imgEl && imgEl.src && !imgEl.src.includes('data:image')) {
                            product.ImageUrl = imgEl.src;
                        }

                        // 7. Prix actuel - extraction simplifiée
                        const priceSelectors = [
                            '.a-price .a-offscreen',
                            '.a-price-whole',
                            '[data-cy=price]',
                            '.a-color-price',
                            'span[class*=price]',
                            '.a-price'
                        ];

                        for (const selector of priceSelectors) {
                            const priceEl = element.querySelector(selector);
                            if (priceEl) {
                                let price = priceEl.textContent.trim();
                                if (price && (price.includes('€') || price.includes('EUR'))) {
                                    product.Price = price;
                                    break;
                                }
                            }
                        }

                        // Fallback: recherche de prix dans le texte
                        if (!product.Price) {
                            const allText = element.textContent || '';
                            const priceMatch = allText.match(/\d+[,.]?\d*\s*€/);
                            if (priceMatch) {
                                product.Price = priceMatch[0];
                            }
                        }

                        // 8. Prix original (barré) - extraction simplifiée
                        const originalPriceSelectors = [
                            '.a-text-price .a-offscreen',
                            '.a-price.a-text-price .a-offscreen',
                            '[data-cy=list-price]',
                            'span[style*=text-decoration]'
                        ];

                        for (const selector of originalPriceSelectors) {
                            const origPriceEl = element.querySelector(selector);
                            if (origPriceEl && origPriceEl.textContent.trim() && origPriceEl.textContent.includes('€')) {
                                product.OriginalPrice = origPriceEl.textContent.trim();
                                break;
                            }
                        }

                        // 9. Réduction - avec nouveau sélecteur prioritaire
                        if (!product.Discount) {
                            const discountSelectors = [
                                // PRIORITÉ: Nouveau sélecteur identifié
                                'div[class*=style_badgeContainer]',
                                'div[class^=style_badgeContainer]',
                                // Sélecteurs existants
                                '.savingsPercentage',
                                '[data-cy=percentage]',
                                'span[class*=badge]',
                                'div[class*=badge]'
                            ];

                            for (const selector of discountSelectors) {
                                const discountEl = element.querySelector(selector);
                                if (discountEl && discountEl.textContent.includes('%')) {
                                    product.Discount = discountEl.textContent.trim();
                                    break;
                                }
                            }
                        }

                        // 10. Note/Rating
                        const ratingSelectors = [
                            '.a-icon-alt',
                            '.a-star-medium .a-icon-alt',
                            '[aria-label*=étoiles]',
                            '[aria-label*=stars]'
                        ];

                        for (const selector of ratingSelectors) {
                            const ratingEl = element.querySelector(selector);
                            if (ratingEl) {
                                const rating = ratingEl.textContent.trim() || ratingEl.getAttribute('aria-label') || '';
                                if (rating && (rating.includes('étoiles') || rating.includes('stars') || rating.match(/\d[,.]?\d*/))) {
                                    product.Rating = rating;
                                    break;
                                }
                            }
                        }

                        // 11. Nombre d'avis
                        const reviewSelectors = [
                            'a[href*=#customerReviews] span',
                            '.a-link-normal span',
                            'span[aria-label*=avis]',
                            'span[aria-label*=reviews]'
                        ];

                        for (const selector of reviewSelectors) {
                            const reviewEl = element.querySelector(selector);
                            if (reviewEl) {
                                const reviewText = reviewEl.textContent.trim();
                                if (reviewText && (reviewText.match(/\d+/) || reviewText.includes('avis'))) {
                                    product.ReviewCount = reviewText;
                                    break;
                                }
                            }
                        }

                        // 12. Catégorie du produit
                        const categorySelectors = [
                            // Fil d'Ariane (breadcrumb)
                            '#wayfinding-breadcrumbs_feature_div a',
                            '.a-breadcrumb a',
                            '[data-cy=breadcrumb] a',
                            // Navigation catégorie
                            '#nav-subnav a.nav-a',
                            '.nav-category-button',
                            // Métadonnées produit
                            '[data-feature-name=breadcrumbs] a',
                            '.a-unordered-list.a-nostyle.a-vertical.a-spacing-none a',
                            // Fallback dans le titre ou description
                            '.a-section h1',
                            '#productTitle'
                        ];

                        for (const selector of categorySelectors) {
                            const categoryElements = element.querySelectorAll ? element.querySelectorAll(selector) : document.querySelectorAll(selector);
                            if (categoryElements.length > 0) {
                                // Prendre la dernière catégorie du fil d'Ariane (la plus spécifique)
                                const lastCategory = categoryElements[categoryElements.length - 1];
                                if (lastCategory && lastCategory.textContent) {
                                    const categoryText = lastCategory.textContent.trim();
                                    if (categoryText && categoryText.length > 2 && categoryText.length < 100 &&
                                        !categoryText.includes('€') && !categoryText.includes('%')) {
                                        product.Category = categoryText;
                                        break;
                                    }
                                }
                            }
                        }

                        // Fallback: extraire catégorie depuis l'URL ou le contexte
                        if (!product.Category && product.ProductUrl) {
                            // Essayer d'extraire depuis l'URL Amazon
                            const urlMatch = product.ProductUrl.match(/\/([^\/]+)\/dp\//);
                            if (urlMatch && urlMatch[1] && urlMatch[1] !== 'gp') {
                                product.Category = urlMatch[1].replace(/-/g, ' ');
                            }
                        }

                        // Validation finale et ajout du produit
                        // Critères minimums : titre ET URL
                        const hasValidTitle = product.Title && product.Title.length > 5;
                        const hasValidUrl = product.ProductUrl && product.ProductUrl.includes('amazon');

                        if (hasValidTitle && hasValidUrl) {
                            products.push(product);

                            // Debug pour les premiers produits
                            if (index < 3) {
                                console.log('✅ Produit ' + (index + 1) + ' extrait:');
                                console.log('   Titre: ' + product.Title.substring(0, 50) + '...');
                                console.log('   URL: ' + product.ProductUrl);
                                console.log('   Prix: ' + (product.Price || 'Non trouvé'));
                                console.log('   Réduction: ' + (product.Discount || 'Non trouvé'));
                                console.log('   Catégorie: ' + (product.Category || 'Non trouvé'));
                            }
                        } else {
                            if (index < 3) {
                                console.log('❌ Produit ' + (index + 1) + ' rejeté - Titre: ' + hasValidTitle + ', URL: ' + hasValidUrl);
                            }
                        }
                    } catch (error) {
                        console.log('Erreur lors de l\'extraction du produit ' + (index + 1) + ':', error);
                    }
                });

                return products;
            }");

            return products?.ToList() ?? new List<ProductInfo>();
        }

        /// <summary>
        /// Navigue vers la page suivante s'il y en a une
        /// </summary>
        private async Task<bool> NavigateToNextPage(IPage page)
        {
            try
            {
                // Chercher le bouton "Suivant" ou "Next"
                var nextButtonFound = await page.EvaluateFunctionAsync<bool>(@"() => {
                    const nextSelectors = [
                        'a[aria-label=Aller à la page suivante]',
                        'a[aria-label=Go to next page]',
                        '.a-pagination .a-last a',
                        'a[aria-label*=suivant]',
                        'a[aria-label*=next]',
                        '.a-pagination li:last-child a:not(.a-disabled)'
                    ];

                    for (const selector of nextSelectors) {
                        const nextButton = document.querySelector(selector);
                        if (nextButton && !nextButton.classList.contains('a-disabled')) {
                            nextButton.click();
                            return true;
                        }
                    }
                    return false;
                }");

                if (nextButtonFound)
                {
                    // Attendre que la nouvelle page se charge
                    await page.WaitForNavigationAsync(new NavigationOptions 
                    { 
                        WaitUntil = new[] { WaitUntilNavigation.Networkidle0 },
                        Timeout = 30000
                    });
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de la navigation vers la page suivante : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Génère les liens d'affiliation Amazon pour tous les produits
        /// </summary>
        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    // Extraire l'ASIN de l'URL
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://amzn.to/{GenerateShortCode(asin)}";
                        // Ou utiliser le format complet : 
                        // product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        /// <summary>
        /// Génère un code court pour les liens d'affiliation (simulation)
        /// Dans un vrai cas, vous utiliseriez l'API Amazon Associates
        /// </summary>
        private string GenerateShortCode(string asin)
        {
            // Simulation d'un code court - remplacez par votre logique réelle
            var hash = asin.GetHashCode();
            return Math.Abs(hash).ToString("X")[..6].ToLower();
        }

        /// <summary>
        /// Génère des posts Facebook pour tous les produits extraits
        /// </summary>
        public async Task GenerateFacebookPostsForAllProducts(string outputDirectory = "facebook_posts")
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            Console.WriteLine($"📝 Génération de {_extractedProducts.Count} posts Facebook...");

            for (int i = 0; i < _extractedProducts.Count; i++)
            {
                var product = _extractedProducts[i];
                
                try
                {
                    Console.WriteLine($"📝 Génération du post {i + 1}/{_extractedProducts.Count} : {product.Title[..Math.Min(50, product.Title.Length)]}...");
                    
                    var post = await _postGenerator.GeneratePostFromUrl(product.ProductUrl, product.AffiliateLink);
                    
                    // Sauvegarder le post
                    var fileName = $"post_{i + 1:D3}_{SanitizeFileName(product.Title[..Math.Min(30, product.Title.Length)])}.txt";
                    var filePath = Path.Combine(outputDirectory, fileName);
                    
                    var postContent = $"Produit: {product.Title}\n";
                    postContent += $"URL: {product.ProductUrl}\n";
                    postContent += $"Lien d'affiliation: {product.AffiliateLink}\n";
                    postContent += $"Prix: {product.Price}\n";
                    postContent += $"Prix original: {product.OriginalPrice}\n";
                    postContent += $"Réduction: {product.Discount}\n";
                    postContent += $"Note: {product.Rating}\n";
                    postContent += $"Avis: {product.ReviewCount}\n";
                    postContent += $"Catégorie: {product.Category}\n";
                    postContent += $"\n=== POST FACEBOOK ===\n\n{post}";
                    
                    await File.WriteAllTextAsync(filePath, postContent);
                    
                    Console.WriteLine($"✅ Post sauvegardé : {fileName}");
                    
                    // Délai entre les générations pour éviter les limites de taux
                    await Task.Delay(2000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur pour le produit {i + 1} : {ex.Message}");
                }
            }

            Console.WriteLine($"🎉 Génération terminée ! {_extractedProducts.Count} posts créés dans le dossier '{outputDirectory}'");
        }

        /// <summary>
        /// Sauvegarde la liste des produits extraits en JSON
        /// </summary>
        public async Task SaveProductsToJson(string filePath = "extracted_products.json")
        {
            var json = JsonSerializer.Serialize(_extractedProducts, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            
            await File.WriteAllTextAsync(filePath, json);
            Console.WriteLine($"💾 Produits sauvegardés dans : {filePath}");
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;

        /// <summary>
        /// Diagnostic de la page pour comprendre pourquoi les produits ne sont pas extraits
        /// </summary>
        private async Task DiagnosePageContent(IPage page)
        {
            try
            {
                var diagnosticInfo = await page.EvaluateFunctionAsync<string>(@"() => {
                    const results = [];

                    // Informations générales sur la page
                    results.push(`URL: ${window.location.href}`);
                    results.push(`Titre: ${document.title}`);
                    results.push(`Total éléments: ${document.querySelectorAll('*').length}`);

                    // Recherche d'éléments avec des attributs data-*
                    const dataElements = document.querySelectorAll('[data-asin], [data-deal-id], [data-testid], [data-component-type]');
                    results.push(`Éléments avec data-*: ${dataElements.length}`);

                    if (dataElements.length > 0) {
                        results.push('Premiers éléments data-* trouvés:');
                        for (let i = 0; i < Math.min(dataElements.length, 5); i++) {
                            const el = dataElements[i];
                            const attrs = Array.from(el.attributes)
                                .filter(attr => attr.name.startsWith('data-'))
                                .map(attr => `${attr.name}='${attr.value}'`)
                                .join(' ');
                            results.push(`  ${el.tagName}: ${attrs}`);
                        }
                    }

                    // Recherche de classes spécifiques
                    const specificClasses = [
                        's-result-item',
                        'deal-card',
                        'DealCard',
                        'a-section',
                        's-card-container'
                    ];

                    specificClasses.forEach(className => {
                        const elements = document.querySelectorAll('.' + className);
                        results.push(`Classe '${className}': ${elements.length} éléments`);
                    });

                    // Recherche de liens vers des produits
                    const productLinks = document.querySelectorAll('a[href*=""/dp/""], a[href*=""/gp/product/""]');
                    results.push(`Liens produits (/dp/ ou /gp/product/): ${productLinks.length}`);

                    // Vérifier si la page contient des messages d'erreur ou de redirection
                    const errorMessages = document.querySelectorAll('.a-alert-error, .error-message, [data-testid=""error""]');
                    if (errorMessages.length > 0) {
                        results.push(`Messages d'erreur détectés: ${errorMessages.length}`);
                    }

                    // Vérifier si c'est une page de captcha
                    const captcha = document.querySelector('form[action*=""captcha""], #captchacharacters');
                    if (captcha) {
                        results.push('CAPTCHA détecté sur la page !');
                    }

                    return results.join('\n');
                }");

                Console.WriteLine("🔍 Diagnostic de la page :");
                Console.WriteLine(diagnosticInfo);

                // Sauvegarder le HTML pour analyse manuelle
                var htmlContent = await page.GetContentAsync();
                var fileName = $"debug_page_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                await File.WriteAllTextAsync(fileName, htmlContent);
                Console.WriteLine($"💾 HTML sauvegardé pour analyse : {fileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du diagnostic : {ex.Message}");
            }
        }
    }
}
