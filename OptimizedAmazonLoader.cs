using PuppeteerSharp;
using System.Collections.Concurrent;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Optimized browser loader with connection pooling and improved resource management
    /// </summary>
    public class OptimizedAmazonLoader : IDisposable
    {
        private IBrowser? _browser;
        private readonly ConcurrentQueue<IPage> _pagePool = new();
        private readonly SemaphoreSlim _browserSemaphore = new(1, 1);
        private readonly AmazonScrapingConfiguration _config;
        private bool _disposed = false;

        // Browser configuration
        private readonly LaunchOptions _launchOptions;
        private readonly string _userDataDir;

        public OptimizedAmazonLoader(AmazonScrapingConfiguration config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            
            _userDataDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "Amazon2FacebookPoster",
                "ChromeUserData"
            );
            
            Directory.CreateDirectory(_userDataDir);

            _launchOptions = new LaunchOptions
            {
                Headless = _config.HeadlessMode,
                UserDataDir = _userDataDir,
                Args = new[]
                {
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--window-size=1920,1080"
                },
                DefaultViewport = new ViewPortOptions
                {
                    Width = 1920,
                    Height = 1080
                },
                Timeout = _config.PageLoadTimeout
            };
        }

        /// <summary>
        /// Initialize browser with optimized settings
        /// </summary>
        public async Task InitializeAsync()
        {
            await _browserSemaphore.WaitAsync();
            try
            {
                if (_browser != null) return;

                Console.WriteLine("🌐 Initializing optimized browser...");
                
                // Download browser if needed
                await new BrowserFetcher().DownloadAsync();
                
                // Launch browser
                _browser = await Puppeteer.LaunchAsync(_launchOptions);
                
                // Pre-warm the page pool with a few pages
                for (int i = 0; i < 2; i++)
                {
                    var page = await CreateOptimizedPageAsync();
                    _pagePool.Enqueue(page);
                }

                Console.WriteLine("✅ Browser initialized with page pool");
            }
            finally
            {
                _browserSemaphore.Release();
            }
        }

        /// <summary>
        /// Get a page from the pool or create a new one
        /// </summary>
        private async Task<IPage> GetPageAsync()
        {
            if (_pagePool.TryDequeue(out var page) && !page.IsClosed)
            {
                return page;
            }

            return await CreateOptimizedPageAsync();
        }

        /// <summary>
        /// Return a page to the pool for reuse
        /// </summary>
        private void ReturnPageToPool(IPage page)
        {
            if (!page.IsClosed && _pagePool.Count < 5) // Limit pool size
            {
                _pagePool.Enqueue(page);
            }
            else
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await page.CloseAsync();
                    }
                    catch { /* Ignore cleanup errors */ }
                });
            }
        }

        /// <summary>
        /// Create an optimized page with performance settings
        /// </summary>
        private async Task<IPage> CreateOptimizedPageAsync()
        {
            if (_browser == null)
                throw new InvalidOperationException("Browser not initialized. Call InitializeAsync() first.");

            var page = await _browser.NewPageAsync();

            // Set user agent to avoid detection
            await page.SetUserAgentAsync(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            );

            // Block unnecessary resources to improve performance
            await page.SetRequestInterceptionAsync(true);
            page.Request += async (sender, e) =>
            {
                var resourceType = e.Request.ResourceType;
                
                // Block images, fonts, and other non-essential resources for faster loading
                if (resourceType == ResourceType.Image || 
                    resourceType == ResourceType.Font ||
                    resourceType == ResourceType.Media ||
                    resourceType == ResourceType.Other)
                {
                    await e.Request.AbortAsync();
                }
                else
                {
                    await e.Request.ContinueAsync();
                }
            };

            // Set timeouts
            page.DefaultTimeout = _config.PageLoadTimeout;
            page.DefaultNavigationTimeout = _config.PageLoadTimeout;

            return page;
        }

        /// <summary>
        /// Load a page and execute an action with optimized error handling
        /// </summary>
        public async Task<T> LoadPageAsync<T>(string url, Func<IPage, Task<T>> action)
        {
            if (_browser == null)
                await InitializeAsync();

            var page = await GetPageAsync();
            var retryCount = 0;
            var maxRetries = _config.MaxRetryAttempts;

            while (retryCount < maxRetries)
            {
                try
                {
                    // Navigate with retry logic
                    await NavigateWithRetryAsync(page, url);
                    
                    // Execute the action
                    var result = await action(page);
                    
                    // Return page to pool for reuse
                    ReturnPageToPool(page);
                    
                    return result;
                }
                catch (Exception ex) when (retryCount < maxRetries - 1)
                {
                    retryCount++;
                    Console.WriteLine($"⚠️ Page load attempt {retryCount} failed: {ex.Message}");
                    Console.WriteLine($"🔄 Retrying in {retryCount * 2} seconds...");
                    
                    await Task.Delay(retryCount * 2000);
                    
                    // Create a new page for retry
                    try
                    {
                        await page.CloseAsync();
                    }
                    catch { /* Ignore cleanup errors */ }
                    
                    page = await CreateOptimizedPageAsync();
                }
            }

            // Final attempt - let exception bubble up
            await NavigateWithRetryAsync(page, url);
            var finalResult = await action(page);
            ReturnPageToPool(page);
            return finalResult;
        }

        /// <summary>
        /// Navigate to URL with retry logic for network issues
        /// </summary>
        private async Task NavigateWithRetryAsync(IPage page, string url)
        {
            var navigationOptions = new NavigationOptions
            {
                WaitUntil = new[] { WaitUntilNavigation.Networkidle0 },
                Timeout = _config.PageLoadTimeout
            };

            try
            {
                await page.GoToAsync(url, navigationOptions);
            }
            catch (NavigationException)
            {
                // Retry with less strict wait condition
                navigationOptions.WaitUntil = new[] { WaitUntilNavigation.DOMContentLoaded };
                await page.GoToAsync(url, navigationOptions);
            }
        }

        /// <summary>
        /// Capture screenshot with optimization
        /// </summary>
        public async Task<string> CaptureScreenshotAsync(IPage page, string productUrl)
        {
            if (!_config.EnableScreenshots) return "";

            try
            {
                var fileName = $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}[..8].png";
                var filePath = Path.Combine(_config.OutputDirectory, "screenshots", fileName);
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);

                await page.ScreenshotAsync(new ScreenshotOptions
                {
                    Path = filePath,
                    FullPage = false, // Only visible area for performance
                    Type = ScreenshotType.Png,
                    Quality = 80 // Reduce file size
                });

                Console.WriteLine($"📸 Screenshot saved: {fileName}");
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Screenshot failed: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Clean up all resources
        /// </summary>
        public async Task CleanupAsync()
        {
            await _browserSemaphore.WaitAsync();
            try
            {
                // Close all pages in pool
                while (_pagePool.TryDequeue(out var page))
                {
                    try
                    {
                        if (!page.IsClosed)
                            await page.CloseAsync();
                    }
                    catch { /* Ignore cleanup errors */ }
                }

                // Close browser
                if (_browser != null)
                {
                    await _browser.CloseAsync();
                    _browser = null;
                }

                Console.WriteLine("🔒 Browser cleanup completed");
            }
            finally
            {
                _browserSemaphore.Release();
            }
        }

        /// <summary>
        /// Dispose pattern implementation
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                CleanupAsync().GetAwaiter().GetResult();
                _browserSemaphore.Dispose();
                _disposed = true;
            }
        }
    }
}
