using PuppeteerSharp;
using System.Text.Json;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur avec scraping direct optimisé et option IA en fallback
    /// Utilise les sélecteurs CSS spécifiques identifiés pour Amazon
    /// </summary>
    public class DirectScrapingExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private readonly AiBasedExtractor? _aiExtractor;
        private readonly List<ProductInfo> _extractedProducts = new();
        private readonly Random _random = new();

        // Configuration
        private const string DEALS_URL = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
        private const int MAX_SCROLL_ATTEMPTS = 20;
        private const int SCROLL_DELAY_MIN = 1000;
        private const int SCROLL_DELAY_MAX = 3000;

        public DirectScrapingExtractor(AmazonLoader<string> amazonLoader, AiBasedExtractor? aiExtractor = null)
        {
            _amazonLoader = amazonLoader;
            _aiExtractor = aiExtractor;
        }

        /// <summary>
        /// Extrait tous les produits en utilisant le scraping direct avec fallback IA
        /// </summary>
        public async Task<List<ProductInfo>> ExtractAllDealsProducts(int maxPages = 5, bool useAiFallback = false)
        {
            _extractedProducts.Clear();

            Console.WriteLine("🚀 DÉMARRAGE DU SCRAPING DIRECT OPTIMISÉ");
            Console.WriteLine($"   📄 Pages maximum: {maxPages}");
            Console.WriteLine($"   🤖 Fallback IA: {(useAiFallback && _aiExtractor != null ? "Activé" : "Désactivé")}");
            Console.WriteLine();

            try
            {
                // Utiliser l'extracteur Amazon existant mais avec les nouveaux sélecteurs
                var amazonExtractor = new AmazonDealsExtractor(_amazonLoader);
                if (useAiFallback && _aiExtractor != null)
                {
                    amazonExtractor.SetGeminiApiKey("dummy-key"); // Pour activer le fallback
                }

                // Extraire les produits avec les sélecteurs améliorés
                var products = await amazonExtractor.ExtractAllDealsProducts(maxPages);

                _extractedProducts.AddRange(products);

                Console.WriteLine();
                Console.WriteLine($"🎯 EXTRACTION TERMINÉE: {_extractedProducts.Count} produits trouvés au total");

                return _extractedProducts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de l'extraction: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// Génère les liens d'affiliation pour tous les produits
        /// </summary>
        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        /// <summary>
        /// Sauvegarde les produits en JSON
        /// </summary>
        public async Task SaveProductsToJson(string filePath)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_extractedProducts, options);
            await File.WriteAllTextAsync(filePath, json);
            Console.WriteLine($"💾 Produits sauvegardés: {filePath}");
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;
    }
}
