using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;
using PuppeteerSharp;
using System.Linq;
using System.Text;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur IA optimisé avec analyse multimodale (HTML + Screenshots)
    /// </summary>
    public class AiBasedExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private FacebookPostGenerator? _postGenerator;
        private readonly List<ProductInfo> _extractedProducts = new();
        private readonly Random _random = new();

        // Configuration
        private const string DEALS_URL = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
        private const int MAX_RETRIES = 20; // Augmenté à 20 tentatives
        private const int RETRY_DELAY_MINUTES = 1;
        private const int MAX_HTML_CHARS = 4_000_000;

        public AiBasedExtractor(AmazonLoader<string> amazonLoader)
        {
            _amazonLoader = amazonLoader;
        }

        public void SetGeminiApiKey(string apiKey) => _postGenerator = new FacebookPostGenerator(apiKey);

        /// <summary>
        /// Point d'entrée principal pour l'extraction IA
        /// </summary>
        public async Task<List<ProductInfo>> ExtractProductsWithAI(int maxPages = 1, int maxLoadMoreClicks = 5)
        {
            ValidateConfiguration();
            _extractedProducts.Clear();

            Console.WriteLine("🤖 Début de l'extraction basée sur l'IA...");
            Console.WriteLine($"📋 Configuration: {maxPages} pages max, {maxLoadMoreClicks} clics 'Afficher plus' max");

            await _amazonLoader.LoadPage(DEALS_URL, async page =>
            {
                var paginationType = await DetectPaginationType(page);
                Console.WriteLine($"🔍 Type de pagination détecté : {paginationType}");

                if (paginationType == "infinite_scroll")
                    await ProcessInfiniteScroll(page, maxPages, maxLoadMoreClicks);
                else
                    await ProcessTraditionalPagination(page, maxPages);

                Console.WriteLine($"🎯 Extraction IA terminée ! Total : {_extractedProducts.Count} produits");
                return "";
            });

            return _extractedProducts;
        }

        /// <summary>
        /// Traite l'infinite scroll avec optimisations
        /// </summary>
        private async Task ProcessInfiniteScroll(IPage page, int maxPages, int maxLoadMoreClicks)
        {
            // Phase 1: Chargement du contenu
            var clickCount = await LoadMoreContent(page, maxLoadMoreClicks);
            
            // Phase 2: Analyse IA par sections
            await AnalyzeContentSections(page, maxPages);
            
            PrintSummary(clickCount, maxLoadMoreClicks, maxPages);
        }

        /// <summary>
        /// Traite la pagination traditionnelle
        /// </summary>
        private async Task ProcessTraditionalPagination(IPage page, int maxPages)
        {
            for (int pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA de la page {pageNum}...");
                
                await ShowPageInfo(page);
                await Task.Delay(5000);
                
                await AnalyzePage(page, pageNum);
                
                if (pageNum < maxPages && !await NavigateToNextPage(page))
                {
                    Console.WriteLine("⚠️ Pas de page suivante trouvée, arrêt de l'extraction");
                    break;
                }
                
                await RandomDelay(2000, 8000);
            }
        }

        /// <summary>
        /// Charge plus de contenu via les boutons "Afficher plus"
        /// </summary>
        private async Task<int> LoadMoreContent(IPage page, int maxClicks)
        {
            Console.WriteLine($"🔄 PHASE 1: Chargement du contenu (max {maxClicks} clics 'Afficher plus')");
            
            int clickCount = 0;
            while (clickCount < maxClicks)
            {
                if (!await ClickLoadMoreButton(page, clickCount, maxClicks))
                {
                    Console.WriteLine("🔍 Plus de boutons 'Afficher plus' trouvés");
                    break;
                }
                
                clickCount++;
                Console.WriteLine($"✅ Clic {clickCount}/{maxClicks} effectué");
                await Task.Delay(5000);
                await RandomDelay(2000, 5000);
            }
            
            return clickCount;
        }

        /// <summary>
        /// Analyse le contenu par sections
        /// </summary>
        private async Task AnalyzeContentSections(IPage page, int maxPages)
        {
            Console.WriteLine($"🧠 PHASE 2: Analyse IA du contenu chargé (max {maxPages} analyses)");
            
            int stableCount = 0;
            for (int pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA section {pageNum}/{maxPages}...");
                
                await ScrollToBottom(page);
                var newProducts = await AnalyzePage(page, pageNum);
                
                if (newProducts == 0)
                {
                    stableCount++;
                    Console.WriteLine($"⚠️ Aucun nouveau produit dans cette analyse ({stableCount}/2)");
                    if (stableCount >= 2)
                    {
                        Console.WriteLine("⚠️ Plus de nouveaux produits détectés, arrêt des analyses");
                        break;
                    }
                }
                else
                {
                    stableCount = 0;
                }
                
                if (pageNum < maxPages) await RandomDelay(3000, 8000);
            }
        }

        /// <summary>
        /// Analyse une page avec capture par sections pour contourner le lazy loading d'Amazon
        /// </summary>
        private async Task<int> AnalyzePage(IPage page, int pageNum)
        {
            Console.WriteLine($"📄 Début de l'analyse de la page/section {pageNum}...");

            // S'assurer que la page est stable avant l'analyse
            await Task.Delay(2000);

            // Vérifier le nombre de produits initiaux
            var initialProductCount = await CountVisibleProducts(page);
            Console.WriteLine($"📦 Produits initialement visibles : {initialProductCount}");

            // NOUVELLE STRATÉGIE : Capturer le HTML par sections pendant le scroll
            var allHtmlSections = await CaptureHtmlBySections(page, pageNum);

            // Combiner tous les HTML capturés
            var combinedHtml = CombineHtmlSections(allHtmlSections);
            var cleanedHtml = CleanHtmlForAI(combinedHtml);

            Console.WriteLine($"📊 HTML combiné de {allHtmlSections.Count} sections : {cleanedHtml.Length:N0} caractères");

            // NE PAS capturer de screenshot pour la liste des deals
            // Les screenshots sont réservés aux pages de produits individuels
            Console.WriteLine($"📝 Extraction de la liste des deals sans screenshot (HTML seulement)");

            var products = await ExtractProductsWithGemini(cleanedHtml, ""); // Pas de screenshot
            var newProducts = FilterNewProducts(products);

            _extractedProducts.AddRange(newProducts);

            Console.WriteLine($"✅ {newProducts.Count} nouveaux produits extraits (analyse {pageNum})");
            Console.WriteLine($"📊 Total cumulé : {_extractedProducts.Count} produits uniques");
            Console.WriteLine($"📈 Sections HTML capturées : {allHtmlSections.Count}");

            ShowProductSamples(newProducts);
            return newProducts.Count;
        }

        /// <summary>
        /// Capture le HTML par sections pendant le scroll pour contourner le lazy loading
        /// NOUVELLE STRATÉGIE: Maintient les produits en mémoire pour éviter leur suppression par Amazon
        /// </summary>
        private async Task<List<string>> CaptureHtmlBySections(IPage page, int pageNum)
        {
            var htmlSections = new List<string>();
            var capturedProducts = new HashSet<string>(); // Pour éviter les doublons

            try
            {
                Console.WriteLine("🔄 Capture HTML par sections avec scroll progressif...");

                // Obtenir la hauteur totale de la page
                var pageHeight = await page.EvaluateFunctionAsync<int>("() => document.body.scrollHeight");
                var viewportHeight = await page.EvaluateFunctionAsync<int>("() => window.innerHeight");

                Console.WriteLine($"📏 Hauteur de page: {pageHeight}px, Viewport: {viewportHeight}px");

                // Scroll progressif par sections avec capture HTML à chaque étape
                var scrollStep = viewportHeight / 2; // Scroll par demi-écran
                var currentPosition = 0;
                var maxScrolls = 20; // Limite de sécurité
                var scrollCount = 0;

                // NOUVELLE STRATÉGIE: Initialiser le stockage des produits
                await page.EvaluateFunctionAsync(@"
                    () => {
                        // Créer un conteneur pour maintenir les produits capturés
                        if (!window.capturedProductsData) {
                            window.capturedProductsData = new Set();
                        }
                        if (!window.storedProductsHtml) {
                            window.storedProductsHtml = [];
                        }
                    }");

                // Capturer le HTML initial (position 0)
                await page.EvaluateFunctionAsync("() => window.scrollTo(0, 0)");
                await Task.Delay(3000);

                // Capturer et stocker les produits initiaux
                await CaptureAndStoreProducts(page);
                var initialHtml = await page.GetContentAsync();
                htmlSections.Add(initialHtml);
                Console.WriteLine($"📄 Section 0 capturée : {initialHtml.Length:N0} caractères");

                while (currentPosition < pageHeight && scrollCount < maxScrolls)
                {
                    scrollCount++;
                    currentPosition += scrollStep;

                    Console.WriteLine($"📜 Scroll {scrollCount}/{maxScrolls} vers position {currentPosition}px...");

                    // Scroll vers la position
                    await page.EvaluateFunctionAsync($"() => window.scrollTo(0, {currentPosition})");

                    // Attendre que les éléments se chargent
                    await Task.Delay(3000);

                    // NOUVELLE STRATÉGIE: Capturer et stocker les nouveaux produits visibles
                    await CaptureAndStoreProducts(page);

                    // Capturer le HTML de cette section
                    var sectionHtml = await page.GetContentAsync();
                    htmlSections.Add(sectionHtml);

                    // Vérifier si de nouveaux produits sont apparus
                    var currentProductCount = await CountVisibleProducts(page);
                    var storedProductCount = await GetStoredProductCount(page);
                    Console.WriteLine($"   📄 Section {scrollCount} capturée : {sectionHtml.Length:N0} caractères");
                    Console.WriteLine($"   📦 Produits visibles: {currentProductCount}, Produits stockés: {storedProductCount}");

                    // Mettre à jour la hauteur de page (elle peut avoir changé)
                    var newPageHeight = await page.EvaluateFunctionAsync<int>("() => document.body.scrollHeight");
                    if (newPageHeight > pageHeight)
                    {
                        pageHeight = newPageHeight;
                        Console.WriteLine($"   📏 Nouvelle hauteur détectée: {pageHeight}px");
                    }

                    // Délai aléatoire pour simuler un comportement humain
                    var randomDelay = new Random().Next(1000, 2000);
                    await Task.Delay(randomDelay);
                }

                // Récupérer tous les produits stockés et créer un HTML consolidé
                var consolidatedHtml = await CreateConsolidatedHtml(page);
                if (!string.IsNullOrEmpty(consolidatedHtml))
                {
                    htmlSections.Add(consolidatedHtml);
                    Console.WriteLine($"📦 HTML consolidé ajouté avec tous les produits stockés");
                }

                Console.WriteLine($"✅ Capture terminée : {htmlSections.Count} sections HTML capturées");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de la capture par sections : {ex.Message}");
            }

            return htmlSections;
        }

        /// <summary>
        /// Combine les sections HTML en extrayant les produits uniques
        /// </summary>
        private string CombineHtmlSections(List<string> htmlSections)
        {
            try
            {
                Console.WriteLine($"🔗 Combinaison de {htmlSections.Count} sections HTML...");

                var allProductLinks = new HashSet<string>();
                var combinedProductsHtml = new StringBuilder();

                foreach (var html in htmlSections)
                {
                    // Extraire les liens de produits de cette section
                    var linkPattern = @"href=""([^""]*\/dp\/[A-Z0-9]{10}[^""]*)""";
                    var matches = System.Text.RegularExpressions.Regex.Matches(html, linkPattern);

                    foreach (System.Text.RegularExpressions.Match match in matches)
                    {
                        var link = match.Groups[1].Value;
                        if (allProductLinks.Add(link)) // Ajouter seulement si nouveau
                        {
                            // Extraire le contexte autour de ce lien
                            var linkPosition = match.Index;
                            var contextStart = Math.Max(0, linkPosition - 2000);
                            var contextEnd = Math.Min(html.Length, linkPosition + match.Length + 2000);
                            var context = html.Substring(contextStart, contextEnd - contextStart);

                            combinedProductsHtml.AppendLine($"<!-- PRODUIT UNIQUE {allProductLinks.Count} -->");
                            combinedProductsHtml.AppendLine(context);
                            combinedProductsHtml.AppendLine();
                        }
                    }
                }

                var result = combinedProductsHtml.ToString();
                Console.WriteLine($"✅ HTML combiné : {allProductLinks.Count} produits uniques, {result.Length:N0} caractères");

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de la combinaison : {ex.Message}");
                // Fallback : retourner le premier HTML si erreur
                return htmlSections.FirstOrDefault() ?? "";
            }
        }

        /// <summary>
        /// Compte le nombre de produits visibles sur la page
        /// </summary>
        private async Task<int> CountVisibleProducts(IPage page)
        {
            try
            {
                return await page.EvaluateFunctionAsync<int>(@"
                    () => {
                        const products = document.querySelectorAll('a[href*=""/dp/""], [data-asin]');
                        return products.length;
                    }");
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Capture un screenshot optimisé avec attente du chargement complet
        /// </summary>
        private async Task<string> CaptureScreenshot(IPage page, int pageNum)
        {
            try
            {
                Console.WriteLine($"📸 Préparation de la capture d'écran (page {pageNum})...");

                // Attendre que la page soit complètement chargée
                await WaitForPageToLoad(page);

                // Position optimale pour la capture (début de page avec quelques produits visibles)
                await page.EvaluateFunctionAsync("() => window.scrollTo(0, 0)");
                await Task.Delay(3000);

                var screenshotsDir = Path.Combine(Directory.GetCurrentDirectory(), "screenshots");
                Directory.CreateDirectory(screenshotsDir);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var filename = $"deals_page_{pageNum}_{timestamp}.png";
                var screenshotPath = Path.Combine(screenshotsDir, filename);

                // Capturer avec options optimisées
                await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                {
                    FullPage = true,
                    Type = ScreenshotType.Png
                    // Note: Quality n'est pas supporté pour PNG, seulement pour JPEG
                });

                // Vérifier que le fichier a été créé et n'est pas vide
                var fileInfo = new FileInfo(screenshotPath);
                if (fileInfo.Exists && fileInfo.Length > 1000) // Au moins 1KB
                {
                    Console.WriteLine($"📸 Screenshot sauvegardé : {filename} ({fileInfo.Length / 1024:N0} KB)");
                    return screenshotPath;
                }
                else
                {
                    Console.WriteLine($"⚠️ Screenshot vide ou trop petit : {filename}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur capture screenshot : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Attend que la page soit complètement chargée et force le chargement de tous les produits
        /// </summary>
        private async Task WaitForPageToLoad(IPage page)
        {
            try
            {
                Console.WriteLine("⏳ Attente du chargement complet de la page...");

                // Attendre un délai initial pour le chargement de base
                await Task.Delay(5000);

                // ÉTAPE 1: Forcer le chargement de tous les produits avec scroll progressif
                await ForceLoadAllProducts(page);

                // ÉTAPE 2: Attendre que les éléments de produits soient visibles
                var maxWaitTime = 30000; // 30 secondes max
                var startTime = DateTime.Now;
                var productCount = 0;

                while ((DateTime.Now - startTime).TotalMilliseconds < maxWaitTime)
                {
                    try
                    {
                        // Vérifier si des produits sont visibles
                        productCount = await page.EvaluateFunctionAsync<int>(@"
                            () => {
                                const products = document.querySelectorAll('a[href*=""/dp/""], [data-asin]');
                                return products.length;
                            }");

                        if (productCount > 5) // Au moins 5 produits pour considérer que la page est chargée
                        {
                            Console.WriteLine($"✅ Page chargée avec {productCount} produits détectés");
                            break;
                        }

                        Console.WriteLine($"⏳ Attente des produits... ({productCount} détectés)");
                        await Task.Delay(3000);
                    }
                    catch
                    {
                        await Task.Delay(3000);
                    }
                }

                // ÉTAPE 3: Scroll final pour s'assurer que tout est chargé
                await FinalScrollToLoadImages(page);

                Console.WriteLine($"✅ Chargement de la page terminé ({productCount} produits finaux)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de l'attente du chargement : {ex.Message}");
                // Continuer même en cas d'erreur avec un délai de sécurité
                await Task.Delay(8000);
            }
        }

        /// <summary>
        /// Force le chargement de tous les produits en scrollant progressivement
        /// </summary>
        private async Task ForceLoadAllProducts(IPage page)
        {
            try
            {
                Console.WriteLine("🔄 Forçage du chargement de tous les produits...");

                // Obtenir la hauteur totale de la page
                var pageHeight = await page.EvaluateFunctionAsync<int>("() => document.body.scrollHeight");
                var viewportHeight = await page.EvaluateFunctionAsync<int>("() => window.innerHeight");

                Console.WriteLine($"📏 Hauteur de page: {pageHeight}px, Viewport: {viewportHeight}px");

                // Scroll progressif par sections
                var scrollStep = viewportHeight / 2; // Scroll par demi-écran
                var currentPosition = 0;
                var maxScrolls = 20; // Limite de sécurité
                var scrollCount = 0;

                while (currentPosition < pageHeight && scrollCount < maxScrolls)
                {
                    scrollCount++;
                    currentPosition += scrollStep;

                    Console.WriteLine($"📜 Scroll {scrollCount}/{maxScrolls} vers position {currentPosition}px...");

                    // Scroll vers la position
                    await page.EvaluateFunctionAsync($"() => window.scrollTo(0, {currentPosition})");

                    // Attendre que les éléments se chargent
                    await Task.Delay(2000);

                    // Vérifier si de nouveaux produits sont apparus
                    var currentProductCount = await page.EvaluateFunctionAsync<int>(@"
                        () => {
                            const products = document.querySelectorAll('a[href*=""/dp/""], [data-asin]');
                            return products.length;
                        }");

                    Console.WriteLine($"   📦 {currentProductCount} produits détectés après scroll {scrollCount}");

                    // Mettre à jour la hauteur de page (elle peut avoir changé)
                    var newPageHeight = await page.EvaluateFunctionAsync<int>("() => document.body.scrollHeight");
                    if (newPageHeight > pageHeight)
                    {
                        pageHeight = newPageHeight;
                        Console.WriteLine($"   📏 Nouvelle hauteur détectée: {pageHeight}px");
                    }

                    // Délai aléatoire pour simuler un comportement humain
                    var randomDelay = new Random().Next(1000, 3000);
                    await Task.Delay(randomDelay);
                }

                Console.WriteLine($"✅ Scroll terminé après {scrollCount} étapes");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du scroll forcé : {ex.Message}");
            }
        }

        /// <summary>
        /// Scroll final pour charger les images et éléments restants
        /// </summary>
        private async Task FinalScrollToLoadImages(IPage page)
        {
            try
            {
                Console.WriteLine("🖼️ Chargement final des images et éléments...");

                // Scroll rapide vers le bas puis vers le haut
                await page.EvaluateFunctionAsync("() => window.scrollTo(0, document.body.scrollHeight)");
                await Task.Delay(3000);

                await page.EvaluateFunctionAsync("() => window.scrollTo(0, 0)");
                await Task.Delay(2000);

                // Scroll au milieu pour une capture optimale
                await page.EvaluateFunctionAsync("() => window.scrollTo(0, document.body.scrollHeight / 4)");
                await Task.Delay(2000);

                Console.WriteLine("✅ Chargement final terminé");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du scroll final : {ex.Message}");
            }
        }

        /// <summary>
        /// Détecte le type de pagination de manière optimisée
        /// </summary>
        private async Task<string> DetectPaginationType(IPage page)
        {
            try
            {
                var hasPagination = await page.QuerySelectorAsync("a[aria-label*='page'], .a-pagination a, .s-pagination-item") != null;
                if (hasPagination)
                {
                    Console.WriteLine("🔍 Pagination traditionnelle détectée");
                    return "traditional";
                }
                
                var loadMoreCount = await CountLoadMoreButtons(page);
                Console.WriteLine($"🔍 Infinite scroll détecté ({loadMoreCount} boutons 'Voir plus')");
                return "infinite_scroll";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection pagination : {ex.Message}");
                return "infinite_scroll";
            }
        }

        /// <summary>
        /// Compte les boutons "Afficher plus" de manière optimisée
        /// </summary>
        private async Task<int> CountLoadMoreButtons(IPage page)
        {
            try
            {
                var buttons = await page.QuerySelectorAllAsync("button, a[role='button']");
                int count = 0;
                
                foreach (var button in buttons)
                {
                    try
                    {
                        var text = await GetElementText(button);
                        if (IsLoadMoreButton(text)) count++;
                    }
                    catch { /* Ignorer */ }
                }
                
                return count;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Clique sur un bouton "Afficher plus"
        /// </summary>
        private async Task<bool> ClickLoadMoreButton(IPage page, int currentClick, int maxClicks)
        {
            try
            {
                await ScrollToBottom(page);
                
                if (currentClick >= maxClicks) return false;
                
                var buttons = await page.QuerySelectorAllAsync("button, a[role='button']");
                
                foreach (var button in buttons)
                {
                    try
                    {
                        var text = await GetElementText(button);
                        if (IsLoadMoreButton(text) && await button.IsVisibleAsync())
                        {
                            Console.WriteLine($"🔄 Clic {currentClick + 1}/{maxClicks} sur bouton '{text}'...");
                            await button.ClickAsync();
                            return true;
                        }
                    }
                    catch { /* Continuer */ }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du clic : {ex.Message}");
                return false;
            }
        }

        // Méthodes utilitaires simplifiées
        private void ValidateConfiguration()
        {
            if (_postGenerator == null)
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
        }

        private async Task ScrollToBottom(IPage page)
        {
            await page.EvaluateFunctionAsync("() => window.scrollTo(0, document.body.scrollHeight)");
            await Task.Delay(2000);
        }

        private async Task RandomDelay(int min, int max)
        {
            var delay = _random.Next(min, max);
            Console.WriteLine($"⏳ Attente de {delay / 1000}s avant la prochaine action...");
            await Task.Delay(delay);
        }

        private async Task<string> GetElementText(IElementHandle element)
        {
            var textProperty = await element.GetPropertyAsync("textContent");
            return (await textProperty.JsonValueAsync<string>())?.Trim() ?? "";
        }

        private static bool IsLoadMoreButton(string text) =>
            !string.IsNullOrEmpty(text) && (text.Contains("Afficher plus") || text.Contains("Voir plus") || 
                                           text.Contains("Charger plus") || text.Contains("Load more"));

        private List<ProductInfo> FilterNewProducts(List<ProductInfo> products) =>
            products.Where(p => !_extractedProducts.Any(existing => 
                existing.ProductUrl == p.ProductUrl || existing.Title == p.Title)).ToList();

        private void ShowProductSamples(List<ProductInfo> newProducts)
        {
            if (newProducts.Count == 0) return;
            
            Console.WriteLine("📋 Nouveaux produits détectés :");
            for (int i = 0; i < Math.Min(3, newProducts.Count); i++)
            {
                var product = newProducts[i];
                Console.WriteLine($"   • {product.Title.Substring(0, Math.Min(60, product.Title.Length))}...");
            }
            if (newProducts.Count > 3)
                Console.WriteLine($"   ... et {newProducts.Count - 3} autres produits");
        }

        private void PrintSummary(int clickCount, int maxClicks, int maxPages)
        {
            Console.WriteLine($"📊 RÉSUMÉ FINAL:");
            Console.WriteLine($"   🔄 Clics 'Afficher plus' effectués: {clickCount}/{maxClicks}");
            Console.WriteLine($"   📦 Total produits uniques: {_extractedProducts.Count}");
        }

        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        /// <summary>
        /// Capture et stocke les produits visibles pour éviter leur suppression par Amazon
        /// </summary>
        private async Task CaptureAndStoreProducts(IPage page)
        {
            try
            {
                await page.EvaluateFunctionAsync(@"
                    () => {
                        // Initialiser le stockage si nécessaire
                        if (!window.capturedProductsData) {
                            window.capturedProductsData = new Set();
                        }

                        // Capturer tous les produits actuellement visibles
                        const productSelectors = [
                            'a[href*=""/dp/""]',
                            '[data-asin]',
                            '.s-result-item',
                            '[data-deal-id]',
                            '.DealCard',
                            '.deal-card'
                        ];

                        let capturedCount = 0;
                        productSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(element => {
                                try {
                                    // Extraire les informations du produit
                                    const productData = {
                                        html: element.outerHTML,
                                        asin: element.getAttribute('data-asin') || '',
                                        href: element.href || element.querySelector('a')?.href || '',
                                        text: element.textContent?.trim() || '',
                                        timestamp: Date.now()
                                    };

                                    // Créer une clé unique pour éviter les doublons
                                    const key = productData.asin || productData.href || productData.text.substring(0, 100);
                                    if (key && !window.capturedProductsData.has(key)) {
                                        window.capturedProductsData.add(key);

                                        // Stocker le HTML complet du produit
                                        if (!window.storedProductsHtml) {
                                            window.storedProductsHtml = [];
                                        }
                                        window.storedProductsHtml.push(productData);
                                        capturedCount++;
                                    }
                                } catch (e) {
                                    console.log('Erreur capture produit:', e);
                                }
                            });
                        });

                        console.log(`Produits capturés dans cette section: ${capturedCount}`);
                        console.log(`Total produits stockés: ${window.storedProductsHtml?.length || 0}`);
                        return capturedCount;
                    }");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de la capture des produits : {ex.Message}");
            }
        }

        /// <summary>
        /// Récupère le nombre de produits stockés en mémoire
        /// </summary>
        private async Task<int> GetStoredProductCount(IPage page)
        {
            try
            {
                return await page.EvaluateFunctionAsync<int>(@"
                    () => {
                        return window.storedProductsHtml?.length || 0;
                    }");
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Crée un HTML consolidé avec tous les produits stockés
        /// </summary>
        private async Task<string> CreateConsolidatedHtml(IPage page)
        {
            try
            {
                var consolidatedHtml = await page.EvaluateFunctionAsync<string>(@"
                    () => {
                        if (!window.storedProductsHtml || window.storedProductsHtml.length === 0) {
                            return '';
                        }

                        let html = '<div id=""consolidated-products"">\n';
                        html += '<!-- PRODUITS CONSOLIDÉS POUR ÉVITER LA SUPPRESSION PAR AMAZON -->\n';

                        window.storedProductsHtml.forEach((product, index) => {
                            html += `<!-- PRODUIT STOCKÉ ${index + 1} -->\n`;
                            html += product.html + '\n';
                        });

                        html += '</div>\n';

                        console.log(`HTML consolidé créé avec ${window.storedProductsHtml.length} produits`);
                        return html;
                    }");

                return consolidatedHtml ?? "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de la création du HTML consolidé : {ex.Message}");
                return "";
            }
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;

        /// <summary>
        /// Affiche les informations de pagination (pour pagination traditionnelle)
        /// </summary>
        private async Task ShowPageInfo(IPage page)
        {
            try
            {
                var pageInfo = await page.EvaluateFunctionAsync<string>(@"
                    () => {
                        const elements = document.querySelectorAll('*');
                        for (const el of elements) {
                            const text = el.textContent || '';
                            const match = text.match(/Page\s+(\d+)\s+sur\s+(\d+)/i);
                            if (match) return `Page ${match[1]} sur ${match[2]} détectée`;
                        }
                        return '';
                    }");

                if (!string.IsNullOrEmpty(pageInfo))
                    Console.WriteLine($"📄 {pageInfo}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection info pagination : {ex.Message}");
            }
        }

        /// <summary>
        /// Navigue vers la page suivante (pagination traditionnelle)
        /// </summary>
        private async Task<bool> NavigateToNextPage(IPage page)
        {
            try
            {
                var nextButton = await page.QuerySelectorAsync("a[aria-label*='Suivant'], a[aria-label*='Next'], .a-pagination .a-last a");
                if (nextButton != null && await nextButton.IsVisibleAsync())
                {
                    Console.WriteLine("➡️ Navigation vers la page suivante...");
                    await nextButton.ClickAsync();
                    await Task.Delay(3000);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur navigation page suivante : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Nettoie le HTML pour l'analyse IA (version optimisée)
        /// </summary>
        private string CleanHtmlForAI(string htmlContent)
        {
            // Supprimer scripts, styles et commentaires
            var cleanedHtml = System.Text.RegularExpressions.Regex.Replace(htmlContent,
                @"<script[^>]*>.*?</script>|<style[^>]*>.*?</style>|<!--.*?-->", "",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);

            // Extraire les sections de produits
            return ExtractProductSections(cleanedHtml);
        }

        /// <summary>
        /// Extrait les sections HTML contenant des produits (version optimisée)
        /// </summary>
        private string ExtractProductSections(string html)
        {
            var productSections = new List<string>();

            // Patterns optimisés pour les liens produits Amazon
            var patterns = new[]
            {
                @"<a[^>]*href[^>]*\/dp\/[A-Z0-9]{10}[^>]*>.*?</a>",
                @"href=""[^""]*\/dp\/[A-Z0-9]{10}[^""]*""",
                @"data-asin=""[A-Z0-9]{10}""",
                @"\/dp\/[A-Z0-9]{10}"
            };

            var allMatches = new List<System.Text.RegularExpressions.Match>();
            foreach (var pattern in patterns)
            {
                var matches = System.Text.RegularExpressions.Regex.Matches(html, pattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);
                allMatches.AddRange(matches.Cast<System.Text.RegularExpressions.Match>());
            }

            Console.WriteLine($"🔍 {allMatches.Count} liens produits détectés dans le HTML");

            // Extraire le contexte pour chaque produit
            foreach (var match in allMatches)
            {
                var linkPosition = match.Index;
                var contextStart = Math.Max(0, linkPosition - 1500);
                var contextEnd = Math.Min(html.Length, linkPosition + match.Length + 1500);
                var context = html.Substring(contextStart, contextEnd - contextStart);
                productSections.Add($"<!-- PRODUIT {productSections.Count + 1} -->\n{context}\n");
            }

            // Optimisation pour respecter la limite Gemini
            var result = string.Join("\n", productSections);
            if (result.Length > MAX_HTML_CHARS)
            {
                var truncatedSections = new List<string>();
                var currentLength = 0;

                foreach (var section in productSections)
                {
                    if (currentLength + section.Length > MAX_HTML_CHARS) break;
                    truncatedSections.Add(section);
                    currentLength += section.Length;
                }

                result = string.Join("\n", truncatedSections) + "\n<!-- TRONQUÉ POUR RESPECTER LA LIMITE GEMINI -->";
                Console.WriteLine($"⚠️ HTML tronqué à {truncatedSections.Count} produits pour respecter la limite Gemini ({MAX_HTML_CHARS:N0} caractères)");
            }
            else
            {
                Console.WriteLine($"✅ HTML complet envoyé à Gemini : {result.Length:N0} caractères sur {MAX_HTML_CHARS:N0} disponibles");
            }

            return result;
        }

        /// <summary>
        /// Extrait les produits avec Gemini (version optimisée avec retry amélioré pour erreurs 429)
        /// STRATÉGIE: 20 tentatives maximum avec 1 minute d'attente entre chaque (20 minutes total)
        /// Mode secours UNIQUEMENT après épuisement des 20 tentatives
        /// </summary>
        private async Task<List<ProductInfo>> ExtractProductsWithGemini(string htmlContent, string screenshotPath = "")
        {
            for (int retryCount = 0; retryCount < MAX_RETRIES; retryCount++)
            {
                try
                {
                    if (retryCount > 0)
                        Console.WriteLine($"🔄 Tentative {retryCount + 1}/{MAX_RETRIES} d'appel à l'API Gemini...");

                    var prompt = CreateOptimizedPrompt(htmlContent);
                    var response = await CallGeminiAPI(prompt, screenshotPath);

                    // Vérification spécifique pour les erreurs 429 et autres erreurs API
                    if (IsErrorResponse(response))
                    {
                        var errorType = GetErrorType(response);

                        if (errorType == "429" || errorType == "RATE_LIMIT")
                        {
                            Console.WriteLine($"⚠️ Erreur 429 détectée (tentative {retryCount + 1}/{MAX_RETRIES})");
                            if (retryCount < MAX_RETRIES - 1)
                            {
                                await WaitWithCountdown();
                                continue;
                            }
                            else
                            {
                                Console.WriteLine($"❌ TOUTES LES 20 TENTATIVES d'erreur 429 ont échoué (20 minutes d'attente total).");
                                Console.WriteLine($"🔧 Passage en mode extraction de secours sans IA...");
                                return ExtractProductsFallback(htmlContent);
                            }
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Erreur API non-429 détectée: {errorType}");
                            if (retryCount < MAX_RETRIES - 1)
                            {
                                await WaitWithCountdown();
                                continue;
                            }
                            else
                            {
                                Console.WriteLine($"⚠️ TOUTES LES {MAX_RETRIES} TENTATIVES ont échoué ({MAX_RETRIES} minutes d'attente total).");
                                Console.WriteLine($"🔧 Passage en mode extraction de secours sans IA...");
                                return ExtractProductsFallback(htmlContent);
                            }
                        }
                    }

                    // Réponse valide reçue
                    Console.WriteLine($"✅ Réponse API valide reçue après {retryCount + 1} tentative(s)");
                    return ParseGeminiResponse(response);
                }
                catch (Exception ex)
                {
                    var isRateLimit = ex.Message.Contains("429") || ex.Message.Contains("rate limit") ||
                                     ex.Message.Contains("RESOURCE_EXHAUSTED") || ex.Message.Contains("quota");

                    if (isRateLimit)
                    {
                        Console.WriteLine($"❌ Exception 429/Rate Limit (tentative {retryCount + 1}/{MAX_RETRIES}) : {ex.Message}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ Erreur lors de l'analyse IA (tentative {retryCount + 1}/{MAX_RETRIES}) : {ex.Message}");
                    }

                    if (retryCount < MAX_RETRIES - 1)
                    {
                        await WaitWithCountdown();
                    }
                    else
                    {
                        Console.WriteLine($"❌ TOUTES LES {MAX_RETRIES} TENTATIVES ont échoué ({MAX_RETRIES} minutes d'attente total).");
                        Console.WriteLine($"🔧 Passage en mode extraction de secours sans IA...");
                        return ExtractProductsFallback(htmlContent);
                    }
                }
            }
            return new List<ProductInfo>();
        }

        /// <summary>
        /// Crée un prompt optimisé pour l'analyse IA
        /// </summary>
        private string CreateOptimizedPrompt(string htmlContent)
        {
            return $@"
🎯 MISSION ULTRA-EXHAUSTIVE : Analyse ce HTML Amazon Deals avec screenshot pour une détection parfaite

OBJECTIF PRINCIPAL : Extraire ABSOLUMENT TOUS LES PRODUITS sans aucune exception.

📸 ANALYSE MULTIMODALE (HTML + IMAGE) :
Tu disposes de DEUX sources d'information complémentaires :
1. Le code HTML complet de la page Amazon
2. Un screenshot visuel de la page pour validation et contexte

STRATÉGIE D'ANALYSE RENFORCÉE :
1. RECHERCHE EXHAUSTIVE DES LIENS (HTML) :
   - Tous les liens /dp/[ASIN] (10 caractères alphanumériques)
   - Tous les liens /gp/product/[ASIN]
   - Liens avec paramètres : ?ref=, ?tag=, etc.
   - Attributs data-asin=""[ASIN]""

2. VALIDATION VISUELLE (SCREENSHOT) :
   - Vérifier que les produits détectés dans le HTML sont bien visibles
   - Identifier des produits supplémentaires visibles mais mal structurés dans le HTML
   - Détecter les badges de promotion, prix barrés, pourcentages de réduction

3. EXTRACTION CONTEXTUELLE INTELLIGENTE :
   - Titre du produit dans les balises <span>, <h3>, <h4>, <a>, <div>
   - Texte alt des images et aria-labels
   - Validation avec l'image pour s'assurer de la cohérence

CONSIGNES CRITIQUES :
- UTILISE l'image pour VALIDER et COMPLÉTER les informations du HTML
- NE JAMAIS ignorer un lien /dp/ même si le contexte semble incomplet
- PRÉFÉRER l'inclusion à l'exclusion en cas de doute

FORMAT DE SORTIE STRICT - JSON UNIQUEMENT :
{{
  ""products"": [
    {{
      ""title"": ""Titre exact ou approximatif du produit"",
      ""url"": ""URL COMPLÈTE avec https://www.amazon.fr/dp/ASIN ou URL relative /dp/ASIN"",
      ""discount"": ""Réduction trouvée ou chaîne vide""
    }}
  ]
}}

IMPORTANT POUR LES URLs :
- PRÉFÉRER les URLs complètes : https://www.amazon.fr/dp/ASIN
- Si URL relative : /dp/ASIN (sera automatiquement complétée)
- TOUJOURS inclure l'ASIN (10 caractères alphanumériques)
- Exemples valides :
  * ""https://www.amazon.fr/dp/B08N5WRWNW""
  * ""/dp/B08N5WRWNW""
  * ""B08N5WRWNW"" (ASIN seul)

HTML VOLUMINEUX À ANALYSER :
{htmlContent}";
        }

        /// <summary>
        /// Appelle l'API Gemini avec gestion optimisée des images
        /// IMPORTANT: Screenshots uniquement pour les pages de produits individuels, PAS pour la liste des deals
        /// </summary>
        private async Task<string> CallGeminiAPI(string prompt, string screenshotPath)
        {
            if (!string.IsNullOrEmpty(screenshotPath) && File.Exists(screenshotPath))
            {
                Console.WriteLine($"📸 Envoi du prompt avec screenshot à Gemini (page produit individuel)...");
                return await (_postGenerator?.CallGeminiAPIWithImage(prompt, screenshotPath) ?? Task.FromResult("Error: _postGenerator is null"));
            }
            else
            {
                Console.WriteLine($"📝 Envoi du prompt texte seul à Gemini (liste des deals - pas de screenshot)...");
                return await (_postGenerator?.CallGeminiAPI(prompt) ?? Task.FromResult("Error: _postGenerator is null"));
            }
        }

        /// <summary>
        /// Vérifie si la réponse contient une erreur
        /// </summary>
        private bool IsErrorResponse(string response)
        {
            return response.Contains("RESOURCE_EXHAUSTED") || response.Contains("QUOTA_EXHAUSTED") ||
                   response.Contains("Error:") || response.Contains("429") || response.Contains("quota") ||
                   response.Contains("rate limit") || response.Contains("billing");
        }

        /// <summary>
        /// Détermine le type d'erreur pour un traitement spécialisé
        /// </summary>
        private string GetErrorType(string response)
        {
            if (response.Contains("429") || response.Contains("rate limit"))
                return "429";
            if (response.Contains("RESOURCE_EXHAUSTED") || response.Contains("QUOTA_EXHAUSTED") || response.Contains("quota"))
                return "QUOTA";
            if (response.Contains("billing"))
                return "BILLING";
            if (response.Contains("Error:"))
                return "GENERAL_ERROR";

            return "UNKNOWN";
        }

        /// <summary>
        /// Attend avec compte à rebours optimisé pour erreurs 429
        /// </summary>
        private async Task WaitWithCountdown()
        {
            var waitTimeSeconds = RETRY_DELAY_MINUTES * 60;
            Console.WriteLine($"⏳ ERREUR 429 - Attente de {RETRY_DELAY_MINUTES} minute(s) avant nouvelle tentative...");
            Console.WriteLine($"🕐 Prochaine tentative dans {waitTimeSeconds} secondes...");
            Console.WriteLine($"💡 Amazon/Gemini limite le taux de requêtes - Patience requise");

            var startTime = DateTime.Now;
            for (int i = waitTimeSeconds; i > 0; i -= 10)
            {
                var elapsed = (DateTime.Now - startTime).TotalSeconds;
                var remaining = Math.Max(0, waitTimeSeconds - elapsed);

                if (remaining <= 0) break;

                Console.WriteLine($"⏳ Attente restante : {remaining:F0} secondes... ({DateTime.Now.AddSeconds(remaining):HH:mm:ss})");
                await Task.Delay(TimeSpan.FromSeconds(Math.Min(10, remaining)));
            }

            Console.WriteLine($"✅ Attente terminée - Reprise des tentatives...");
        }

        /// <summary>
        /// Parse la réponse JSON de Gemini (version optimisée)
        /// </summary>
        private List<ProductInfo> ParseGeminiResponse(string response)
        {
            try
            {
                Console.WriteLine($"📥 Réponse Gemini reçue : {response.Length:N0} caractères");

                // Nettoyer la réponse
                var cleanResponse = response.Trim();
                if (cleanResponse.StartsWith("```json")) cleanResponse = cleanResponse.Substring(7);
                if (cleanResponse.EndsWith("```")) cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
                cleanResponse = cleanResponse.Trim();

                var jsonDoc = JsonDocument.Parse(cleanResponse);
                var productsArray = jsonDoc.RootElement.GetProperty("products");
                var products = new List<ProductInfo>();

                foreach (var productElement in productsArray.EnumerateArray())
                {
                    var product = new ProductInfo
                    {
                        Title = GetJsonString(productElement, "title"),
                        ProductUrl = GetJsonString(productElement, "url"),
                        Discount = GetJsonString(productElement, "discount"),
                        IsDeal = true,
                        Price = "", OriginalPrice = "", Rating = "", ReviewCount = "", ImageUrl = "", AffiliateLink = ""
                    };

                    if (!string.IsNullOrEmpty(product.Title) && !string.IsNullOrEmpty(product.ProductUrl))
                    {
                        // Normaliser l'URL pour qu'elle soit complète
                        product.ProductUrl = NormalizeProductUrl(product.ProductUrl);

                        products.Add(product);
                        Console.WriteLine($"✅ Produit extrait: {product.Title.Substring(0, Math.Min(50, product.Title.Length))}...");
                        Console.WriteLine($"   🔗 URL: {product.ProductUrl}");
                    }
                }

                Console.WriteLine($"✅ {products.Count} produits parsés depuis la réponse Gemini");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du parsing JSON : {ex.Message}");
                Console.WriteLine($"Réponse reçue : {response.Substring(0, Math.Min(500, response.Length))}...");
                return new List<ProductInfo>();
            }
        }

        /// <summary>
        /// Extraction de secours sans IA (version optimisée)
        /// </summary>
        private List<ProductInfo> ExtractProductsFallback(string htmlContent)
        {
            var products = new List<ProductInfo>();
            try
            {
                var linkPattern = @"href=""([^""]*\/dp\/[A-Z0-9]{10}[^""]*)""";
                var matches = System.Text.RegularExpressions.Regex.Matches(htmlContent, linkPattern);
                Console.WriteLine($"🔧 Mode secours : {matches.Count} liens /dp/ détectés");

                foreach (System.Text.RegularExpressions.Match match in matches.Take(100))
                {
                    var url = match.Groups[1].Value;

                    // Utiliser la même méthode de normalisation
                    url = NormalizeProductUrl(url);

                    var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        var title = ExtractTitleFromContext(htmlContent, match.Index, asin);

                        var product = new ProductInfo
                        {
                            Title = title, ProductUrl = url, Discount = "", IsDeal = true,
                            Price = "", OriginalPrice = "", Rating = "", ReviewCount = "", ImageUrl = "", AffiliateLink = ""
                        };

                        if (!products.Any(p => p.ProductUrl == product.ProductUrl))
                        {
                            products.Add(product);
                            Console.WriteLine($"🔧 Produit secours: {title.Substring(0, Math.Min(40, title.Length))}...");
                            Console.WriteLine($"   🔗 URL: {url}");
                        }
                    }
                }

                Console.WriteLine($"🔧 Mode secours : {products.Count} produits uniques extraits");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur en mode secours : {ex.Message}");
            }
            return products;
        }

        /// <summary>
        /// Extrait un titre approximatif du contexte HTML
        /// </summary>
        private string ExtractTitleFromContext(string htmlContent, int linkPosition, string asin)
        {
            try
            {
                var contextStart = Math.Max(0, linkPosition - 500);
                var contextEnd = Math.Min(htmlContent.Length, linkPosition + 500);
                var context = htmlContent.Substring(contextStart, contextEnd - contextStart);

                var titlePatterns = new[] { @"alt=""([^""]{10,100})""", @"title=""([^""]{10,100})""", @">([^<]{10,100})<" };

                foreach (var pattern in titlePatterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(context, pattern);
                    if (match.Success)
                    {
                        var title = match.Groups[1].Value.Trim();
                        if (title.Length > 10 && !title.Contains("http") && !title.Contains("amazon"))
                            return title;
                    }
                }
                return $"Produit Amazon {asin}";
            }
            catch
            {
                return $"Produit Amazon {asin}";
            }
        }

        /// <summary>
        /// Normalise une URL de produit pour qu'elle soit complète et valide
        /// </summary>
        private string NormalizeProductUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            // Nettoyer l'URL
            url = url.Trim();

            // Si l'URL est déjà complète et valide
            if (url.StartsWith("https://www.amazon.fr/") && url.Contains("/dp/"))
                return url;

            // Si l'URL commence par /dp/ (URL relative)
            if (url.StartsWith("/dp/"))
                return "https://www.amazon.fr" + url;

            // Si l'URL commence par /gp/product/ (URL relative alternative)
            if (url.StartsWith("/gp/product/"))
                return "https://www.amazon.fr" + url;

            // Si l'URL contient seulement l'ASIN (10 caractères alphanumériques)
            var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"^([A-Z0-9]{10})$");
            if (asinMatch.Success)
                return $"https://www.amazon.fr/dp/{asinMatch.Groups[1].Value}";

            // Si l'URL contient un ASIN quelque part
            var asinInUrlMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            if (asinInUrlMatch.Success)
            {
                var asin = asinInUrlMatch.Groups[1].Value;
                return $"https://www.amazon.fr/dp/{asin}";
            }

            // Si l'URL commence par amazon.fr sans https
            if (url.StartsWith("amazon.fr/") || url.StartsWith("www.amazon.fr/"))
                return "https://www." + url.Replace("www.", "");

            // Si l'URL est relative mais ne commence pas par /
            if (!url.StartsWith("http") && !url.StartsWith("/") && url.Contains("dp/"))
                return "https://www.amazon.fr/" + url;

            // Dernière tentative : extraire l'ASIN de n'importe où dans l'URL
            var anyAsinMatch = System.Text.RegularExpressions.Regex.Match(url, @"([A-Z0-9]{10})");
            if (anyAsinMatch.Success)
            {
                var asin = anyAsinMatch.Groups[1].Value;
                Console.WriteLine($"⚠️ URL non standard détectée, extraction ASIN: {asin}");
                return $"https://www.amazon.fr/dp/{asin}";
            }

            Console.WriteLine($"⚠️ URL non reconnue: {url}");
            return url; // Retourner l'URL originale si aucune normalisation possible
        }

        private string GetJsonString(JsonElement element, string propertyName)
        {
            try
            {
                return element.TryGetProperty(propertyName, out var property) ? property.GetString() ?? "" : "";
            }
            catch
            {
                return "";
            }
        }
    }
}
