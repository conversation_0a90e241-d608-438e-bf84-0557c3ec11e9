using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using PuppeteerSharp;
using System.Text.Json;
using System.Linq;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Processeur complet qui combine scraping IA, extraction des détails produits,
    /// récupération des liens d'affiliation et génération des posts Facebook
    /// </summary>
    public class CompleteAiProcessor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private readonly AiBasedExtractor _aiExtractor;
        private readonly FacebookPostGenerator _postGenerator;
        private readonly ProductDetailExtractor _detailExtractor;
        private readonly Random _random;
        private List<ProductInfo> _products;
        private string _amazonAssociateTag;

        public CompleteAiProcessor(string geminiApiKey, string amazonAssociateTag = "votre-tag-20")
        {
            _amazonLoader = new AmazonLoader<string>();
            _aiExtractor = new AiBasedExtractor(_amazonLoader);
            _postGenerator = new FacebookPostGenerator(geminiApiKey);
            _detailExtractor = new ProductDetailExtractor();
            _random = new Random();
            _products = new List<ProductInfo>();
            _amazonAssociateTag = amazonAssociateTag;

            _amazonLoader.SetGeminiApiKey(geminiApiKey);
            _aiExtractor.SetGeminiApiKey(geminiApiKey);
        }

        /// <summary>
        /// Processus complet : Scraping -> Détails -> Affiliation -> Posts
        /// </summary>
        public async Task<ProcessResult> ProcessCompleteWorkflow(int maxPages = 3, bool headless = false)
        {
            var result = new ProcessResult();
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            try
            {
                Console.WriteLine("🚀 DÉMARRAGE DU PROCESSUS COMPLET IA");
                Console.WriteLine("=" + new string('=', 50));
                Console.WriteLine();

                // Étape 1: Scraping de la liste avec pagination (configuration optimisée)
                Console.WriteLine("📋 ÉTAPE 1: Scraping de la liste des deals (mode optimisé)");
                await _amazonLoader.StartBrowser(headless: headless);

                // Configuration ULTRA-OPTIMISÉE avec limite Gemini 1M+ tokens
                var maxLoadMoreClicks = 20; // Augmentation massive grâce à la capacité Gemini étendue
                Console.WriteLine($"🚀 Configuration ULTRA-OPTIMISÉE (Gemini 1M+ tokens) : {maxPages} pages, {maxLoadMoreClicks} clics 'Afficher plus' max");
                Console.WriteLine($"📊 Capacité théorique : ~4M caractères HTML analysables par l'IA");

                _products = await _aiExtractor.ExtractProductsWithAI(maxPages: maxPages, maxLoadMoreClicks: maxLoadMoreClicks);
                result.ProductsFound = _products.Count;
                
                Console.WriteLine($"✅ {_products.Count} produits trouvés dans la liste");
                
                if (_products.Count == 0)
                {
                    Console.WriteLine("❌ Aucun produit trouvé, arrêt du processus");
                    return result;
                }

                // Sauvegarder la liste initiale
                await SaveProductsToJson(_products, $"deals_list_{timestamp}.json");

                // Étape 2: Extraction des détails de chaque produit
                Console.WriteLine();
                Console.WriteLine("🔍 ÉTAPE 2: Extraction des détails de chaque produit");
                await ExtractProductDetails();

                // Étape 3: Les liens d'affiliation sont déjà récupérés dans l'étape 2
                Console.WriteLine();
                Console.WriteLine("✅ ÉTAPE 3: Liens d'affiliation récupérés lors de l'extraction des détails");

                // Sauvegarder les produits complets
                await SaveProductsToJson(_products, $"complete_products_{timestamp}.json");

                // Étape 4: Génération des posts Facebook
                Console.WriteLine();
                Console.WriteLine("📝 ÉTAPE 4: Génération des posts Facebook");
                await GenerateFacebookPosts(timestamp);

                result.Success = true;
                result.PostsGenerated = _products.Where(p => !string.IsNullOrEmpty(p.FacebookPost)).Count();

                Console.WriteLine();
                Console.WriteLine("🎉 PROCESSUS COMPLET TERMINÉ !");
                Console.WriteLine($"   Produits trouvés: {result.ProductsFound}");
                Console.WriteLine($"   Posts générés: {result.PostsGenerated}");

                await _amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans le processus complet : {ex.Message}");
                result.Error = ex.Message;
                await _amazonLoader.CloseBrowser();
            }

            return result;
        }

        /// <summary>
        /// Extrait les détails manquants de chaque produit en visitant sa page
        /// </summary>
        private async Task ExtractProductDetails()
        {
            var successCount = 0;
            var errorCount = 0;

            for (int i = 0; i < _products.Count; i++)
            {
                var product = _products[i];
                Console.WriteLine($"🔍 Produit {i + 1}/{_products.Count}: {product.Title.Substring(0, Math.Min(50, product.Title.Length))}...");

                try
                {
                    // Délai aléatoire pour simuler navigation humaine (1-20 secondes)
                    var delay = _random.Next(1000, 20000);
                    Console.WriteLine($"   ⏳ Attente de {delay / 1000}s (simulation humaine)...");
                    await Task.Delay(delay);

                    // Afficher progression
                    var progress = (i + 1) * 100 / _products.Count;
                    Console.WriteLine($"   📊 Progression: {progress}% ({i + 1}/{_products.Count})");

                    // Utiliser le ProductDetailExtractor pour une extraction complète
                    await _amazonLoader.LoadPage(product.ProductUrl, async (page) =>
                    {
                        // Extraction complète avec scroll et récupération du lien d'affiliation
                        var updatedProduct = await _detailExtractor.ExtractCompleteProductDetails(page, product, _amazonAssociateTag);

                        // Mettre à jour le produit dans la liste
                        _products[i] = updatedProduct;

                        Console.WriteLine($"   ✅ Extraction complète terminée - Prix: {updatedProduct.Price}, Note: {updatedProduct.Rating}, Commission: {updatedProduct.CommissionRate}");
                        successCount++;
                        return "";
                    });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Erreur extraction détails: {ex.Message}");
                    errorCount++;
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 Résumé extraction détails:");
            Console.WriteLine($"   ✅ Succès: {successCount}/{_products.Count}");
            Console.WriteLine($"   ❌ Erreurs: {errorCount}/{_products.Count}");
        }

        /// <summary>
        /// Génère les posts Facebook pour tous les produits
        /// </summary>
        private async Task GenerateFacebookPosts(string timestamp)
        {
            var outputDir = $"facebook_posts_{timestamp}";
            Directory.CreateDirectory(outputDir);

            var successCount = 0;
            var errorCount = 0;

            for (int i = 0; i < _products.Count; i++)
            {
                var product = _products[i];
                Console.WriteLine($"📝 Post {i + 1}/{_products.Count}: {product.Title.Substring(0, Math.Min(40, product.Title.Length))}...");

                try
                {
                    // Délai pour éviter de surcharger l'API
                    if (i > 0)
                    {
                        var delay = _random.Next(1000, 3000);
                        await Task.Delay(delay);
                    }

                    // Générer le post avec l'IA
                    var post = await _postGenerator.GeneratePostFromUrl(product.ProductUrl, product.AffiliateLink);
                    product.FacebookPost = post;

                    // Sauvegarder le post
                    var filename = $"post_{i + 1:D3}_{SanitizeFilename(product.Title.Substring(0, Math.Min(30, product.Title.Length)))}.txt";
                    var filepath = Path.Combine(outputDir, filename);
                    
                    var postContent = $"Produit: {product.Title}\n";
                    postContent += $"URL: {product.ProductUrl}\n";
                    postContent += $"Lien affilié: {product.AffiliateLink}\n";
                    postContent += $"Prix: {product.Price}\n";
                    postContent += $"Réduction: {product.Discount}\n";
                    postContent += $"Taux de commission: {product.CommissionRate}\n\n";
                    postContent += "=== POST FACEBOOK ===\n\n";
                    postContent += post;

                    await File.WriteAllTextAsync(filepath, postContent);
                    Console.WriteLine($"   ✅ Post généré et sauvegardé");
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Erreur génération post: {ex.Message}");
                    errorCount++;
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 Résumé génération posts:");
            Console.WriteLine($"   ✅ Posts générés: {successCount}/{_products.Count}");
            Console.WriteLine($"   ❌ Erreurs: {errorCount}/{_products.Count}");
            Console.WriteLine($"📁 Posts sauvegardés dans le dossier: {outputDir}");
        }

        private async Task SaveProductsToJson(List<ProductInfo> products, string filename)
        {
            try
            {
                var json = JsonSerializer.Serialize(products, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filename, json);
                Console.WriteLine($"💾 Produits sauvegardés: {filename}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur sauvegarde JSON: {ex.Message}");
            }
        }

        private string SanitizeFilename(string filename)
        {
            var invalid = Path.GetInvalidFileNameChars();
            return string.Join("_", filename.Split(invalid, StringSplitOptions.RemoveEmptyEntries));
        }
    }

    /// <summary>
    /// Résultat du processus complet
    /// </summary>
    public class ProcessResult
    {
        public bool Success { get; set; }
        public int ProductsFound { get; set; }
        public int PostsGenerated { get; set; }
        public string Error { get; set; } = "";
    }
}
