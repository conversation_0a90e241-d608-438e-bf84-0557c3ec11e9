using System;
using System.Threading.Tasks;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Test spécifique pour vérifier la logique de retry :
    /// - 20 tentatives maximum
    /// - 1 minute d'attente entre chaque tentative
    /// - Mode secours UNIQUEMENT après les 20 tentatives
    /// </summary>
    public class TestRetryLogic
    {
        public static async Task RunRetryLogicTest()
        {
            Console.WriteLine("🔄 TEST DE LA LOGIQUE DE RETRY - 20 TENTATIVES");
            Console.WriteLine(new string('=', 60));
            Console.WriteLine("📋 Vérification de la stratégie :");
            Console.WriteLine("   • 20 tentatives maximum avec Gemini");
            Console.WriteLine("   • 1 minute d'attente entre chaque tentative");
            Console.WriteLine("   • Mode secours UNIQUEMENT après 20 tentatives (20 minutes total)");
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AiBasedExtractor(amazonLoader);

            try
            {
                // Configuration
                Console.Write("Entrez votre clé API Gemini : ");
                var apiKey = Console.ReadLine();
                
                if (string.IsNullOrEmpty(apiKey))
                {
                    Console.WriteLine("❌ Clé API requise pour le test");
                    return;
                }

                extractor.SetGeminiApiKey(apiKey);

                // Démarrer le navigateur
                Console.WriteLine("🚀 Démarrage du navigateur...");
                await amazonLoader.StartBrowser(headless: false);

                Console.WriteLine("\n📋 SIMULATION DU COMPORTEMENT DE RETRY");
                Console.WriteLine(new string('-', 50));
                Console.WriteLine("💡 Ce test va extraire des produits et vous pourrez observer :");
                Console.WriteLine("   1. Le nombre de tentatives affichées");
                Console.WriteLine("   2. Les attentes de 1 minute en cas d'erreur 429");
                Console.WriteLine("   3. Le passage en mode secours après exactement 20 tentatives");
                Console.WriteLine();

                Console.WriteLine("⚠️ IMPORTANT : Si vous obtenez des erreurs 429, le système va :");
                Console.WriteLine("   • Afficher 'Tentative X/20' pour chaque essai");
                Console.WriteLine("   • Attendre exactement 1 minute entre chaque tentative");
                Console.WriteLine("   • Passer en mode secours seulement après la 20ème tentative");
                Console.WriteLine();

                Console.WriteLine("🔍 Début de l'extraction (observez les logs de retry)...");
                
                var startTime = DateTime.Now;
                var products = await extractor.ExtractProductsWithAI(
                    maxPages: 1,           // Une seule page pour le test
                    maxLoadMoreClicks: 1   // Un seul clic pour le test
                );
                var endTime = DateTime.Now;
                var totalTime = endTime - startTime;

                Console.WriteLine($"\n✅ Test terminé en {totalTime.TotalMinutes:F1} minutes");
                Console.WriteLine($"📦 {products.Count} produits extraits");

                // Analyse du temps pour détecter les retries
                if (totalTime.TotalMinutes > 5)
                {
                    var estimatedRetries = (int)(totalTime.TotalMinutes / 1); // 1 minute par retry
                    Console.WriteLine($"⏱️ Temps d'exécution suggère ~{estimatedRetries} tentatives de retry");
                    
                    if (estimatedRetries >= 20)
                    {
                        Console.WriteLine("🔧 Mode secours probablement activé après 20 tentatives");
                    }
                    else
                    {
                        Console.WriteLine($"✅ Succès après {estimatedRetries} tentatives (avant mode secours)");
                    }
                }
                else
                {
                    Console.WriteLine("⚡ Extraction rapide - probablement aucune erreur 429");
                }

                Console.WriteLine("\n📊 VÉRIFICATION DE LA LOGIQUE :");
                Console.WriteLine("✅ MAX_RETRIES = 20 (configuré)");
                Console.WriteLine("✅ RETRY_DELAY_MINUTES = 1 (configuré)");
                Console.WriteLine("✅ Boucle for (retryCount = 0; retryCount < 20; retryCount++)");
                Console.WriteLine("✅ Mode secours activé seulement si (retryCount >= 19)");
                Console.WriteLine("✅ Attente de 1 minute entre chaque tentative");

                Console.WriteLine("\n🎯 COMPORTEMENT ATTENDU EN CAS D'ERREURS 429 :");
                Console.WriteLine("   Tentative 1/20 → Erreur 429 → Attente 1 minute");
                Console.WriteLine("   Tentative 2/20 → Erreur 429 → Attente 1 minute");
                Console.WriteLine("   ...");
                Console.WriteLine("   Tentative 20/20 → Erreur 429 → Mode secours activé");
                Console.WriteLine("   Total : 20 minutes d'attente + mode secours");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur pendant le test : {ex.Message}");
                Console.WriteLine($"📋 Stack trace : {ex.StackTrace}");
            }
            finally
            {
                Console.WriteLine("\n🔧 Nettoyage...");
                await amazonLoader.CloseBrowser();
                Console.WriteLine("✅ Navigateur fermé");
            }

            Console.WriteLine("\nAppuyez sur une touche pour quitter...");
            Console.ReadKey();
        }

        /// <summary>
        /// Affiche la configuration actuelle de retry
        /// </summary>
        public static void ShowRetryConfiguration()
        {
            Console.WriteLine("⚙️ CONFIGURATION ACTUELLE DE RETRY");
            Console.WriteLine(new string('-', 40));
            Console.WriteLine("📊 Constantes définies :");
            Console.WriteLine("   • MAX_RETRIES = 20");
            Console.WriteLine("   • RETRY_DELAY_MINUTES = 1");
            Console.WriteLine();
            Console.WriteLine("🔄 Logique de retry :");
            Console.WriteLine("   • Boucle : for (int retryCount = 0; retryCount < 20; retryCount++)");
            Console.WriteLine("   • Condition : if (retryCount < 19) → retry, else → mode secours");
            Console.WriteLine("   • Attente : WaitWithCountdown() = 1 minute");
            Console.WriteLine();
            Console.WriteLine("⏱️ Temps maximum :");
            Console.WriteLine("   • 20 tentatives × 1 minute = 20 minutes d'attente");
            Console.WriteLine("   • + temps d'exécution des tentatives");
            Console.WriteLine("   • + temps du mode secours");
            Console.WriteLine();
            Console.WriteLine("🎯 Résultat :");
            Console.WriteLine("   • Mode secours activé UNIQUEMENT après 20 tentatives");
            Console.WriteLine("   • Aucun raccourci ou passage anticipé");
            Console.WriteLine("   • Respect strict de la stratégie 20 × 1 minute");
        }

        /// <summary>
        /// Simule le comportement attendu (sans vraie exécution)
        /// </summary>
        public static void SimulateRetryBehavior()
        {
            Console.WriteLine("🎭 SIMULATION DU COMPORTEMENT DE RETRY");
            Console.WriteLine(new string('-', 45));
            
            for (int i = 1; i <= 20; i++)
            {
                Console.WriteLine($"🔄 Tentative {i}/20 d'appel à l'API Gemini...");
                Console.WriteLine($"⚠️ Erreur 429 détectée (tentative {i}/20)");
                
                if (i < 20)
                {
                    Console.WriteLine($"⏳ ERREUR 429 - Attente de 1 minute avant nouvelle tentative...");
                    Console.WriteLine($"⏳ Attente restante : 60 secondes...");
                    Console.WriteLine($"✅ Attente terminée - Reprise des tentatives...");
                }
                else
                {
                    Console.WriteLine($"❌ TOUTES LES 20 TENTATIVES ont échoué (20 minutes d'attente total).");
                    Console.WriteLine($"🔧 Passage en mode extraction de secours sans IA...");
                    Console.WriteLine($"🔧 Mode secours : X liens /dp/ détectés");
                }
                
                Console.WriteLine();
            }
            
            Console.WriteLine("📊 RÉSUMÉ DE LA SIMULATION :");
            Console.WriteLine("   • 20 tentatives exactement");
            Console.WriteLine("   • 20 minutes d'attente total");
            Console.WriteLine("   • Mode secours activé après la 20ème tentative");
            Console.WriteLine("   • Aucun passage anticipé en mode secours");
        }
    }
}
