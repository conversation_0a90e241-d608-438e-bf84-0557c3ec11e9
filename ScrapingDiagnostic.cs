using Amazon2FacebookPoster;
using PuppeteerSharp;

namespace Amazon2FacebookPoster.Diagnostics
{
    /// <summary>
    /// Outil de diagnostic pour identifier les problèmes de scraping
    /// </summary>
    public class ScrapingDiagnostic
    {
        /// <summary>
        /// Test complet du scraping des offres Amazon
        /// </summary>
        public static async Task DiagnoseScrapingIssues()
        {
            Console.WriteLine("🔍 DIAGNOSTIC DU SCRAPING AMAZON");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();
            
            try
            {
                // 1. Test de démarrage du navigateur
                Console.WriteLine("1️⃣ Test de démarrage du navigateur...");
                await amazonLoader.StartBrowser(headless: false); // Mode visible pour debug
                Console.WriteLine("✅ Navigateur démarré avec succès");
                Console.WriteLine();

                // 2. Test de navigation vers la page des offres
                Console.WriteLine("2️⃣ Test de navigation vers la page des offres...");
                var dealsUrl = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
                
                await amazonLoader.LoadPage(dealsUrl, async (page) =>
                {
                    Console.WriteLine($"✅ Page chargée : {page.Url}");
                    Console.WriteLine($"📄 Titre de la page : {await page.GetTitleAsync()}");
                    
                    // 3. Test de détection des éléments de produits
                    Console.WriteLine();
                    Console.WriteLine("3️⃣ Test de détection des éléments de produits...");
                    
                    var productSelectors = new[]
                    {
                        "[data-testid=\"deal-card\"]",
                        ".DealCard-module__card",
                        ".a-section.octopus-dlp-asin-section",
                        ".s-result-item[data-asin]",
                        ".deal-card",
                        "[data-deal-id]",
                        "[data-asin]:not([data-asin=\"\"])"
                    };

                    foreach (var selector in productSelectors)
                    {
                        try
                        {
                            var elements = await page.QuerySelectorAllAsync(selector);
                            Console.WriteLine($"   {selector}: {elements.Length} éléments trouvés");
                            
                            if (elements.Length > 0)
                            {
                                // Analyser le premier élément trouvé
                                var firstElement = elements[0];
                                var innerHTML = await firstElement.EvaluateFunctionAsync<string>("el => el.innerHTML.substring(0, 200)");
                                Console.WriteLine($"   Premier élément (extrait): {innerHTML}...");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   {selector}: Erreur - {ex.Message}");
                        }
                    }

                    // 4. Test d'extraction manuelle
                    Console.WriteLine();
                    Console.WriteLine("4️⃣ Test d'extraction manuelle des données...");
                    
                    var extractedData = await page.EvaluateFunctionAsync<string>(@"() => {
                        const results = [];
                        
                        // Essayer différents sélecteurs
                        const selectors = [
                            '[data-testid=""deal-card""]',
                            '.DealCard-module__card',
                            '.a-section.octopus-dlp-asin-section',
                            '.s-result-item[data-asin]',
                            '.deal-card',
                            '[data-deal-id]',
                            '[data-asin]:not([data-asin=""""])'
                        ];
                        
                        for (const selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                results.push(`${selector}: ${elements.length} éléments`);
                                
                                // Analyser le premier élément
                                const first = elements[0];
                                const title = first.querySelector('h3, h2, .a-size-base-plus, .a-size-mini span, [data-cy=""title""]');
                                const link = first.querySelector('a[href*=""/dp/""], a[href*=""/gp/product/""]');
                                const price = first.querySelector('.a-price-whole, .a-price .a-offscreen, [data-cy=""price""]');
                                
                                results.push(`  Titre: ${title ? title.textContent.trim().substring(0, 50) : 'Non trouvé'}`);
                                results.push(`  Lien: ${link ? 'Trouvé' : 'Non trouvé'}`);
                                results.push(`  Prix: ${price ? price.textContent.trim() : 'Non trouvé'}`);
                                break;
                            }
                        }
                        
                        if (results.length === 0) {
                            results.push('Aucun produit détecté avec les sélecteurs standards');
                            
                            // Essayer de détecter la structure de la page
                            const allDivs = document.querySelectorAll('div');
                            results.push(`Total de divs sur la page: ${allDivs.length}`);
                            
                            const withDataAttrs = document.querySelectorAll('[data-asin], [data-deal-id], [data-testid]');
                            results.push(`Éléments avec attributs data-*: ${withDataAttrs.length}`);
                        }
                        
                        return results.join('\n');
                    }");

                    Console.WriteLine("Résultats de l'extraction :");
                    Console.WriteLine(extractedData);

                    // 5. Sauvegarder le HTML pour analyse
                    Console.WriteLine();
                    Console.WriteLine("5️⃣ Sauvegarde du HTML pour analyse...");
                    var htmlContent = await page.GetContentAsync();
                    var fileName = $"amazon_deals_debug_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                    await File.WriteAllTextAsync(fileName, htmlContent);
                    Console.WriteLine($"✅ HTML sauvegardé dans : {fileName}");

                    // 6. Test de capture d'écran
                    Console.WriteLine();
                    Console.WriteLine("6️⃣ Capture d'écran pour analyse visuelle...");
                    var screenshotPath = $"amazon_deals_screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    await page.ScreenshotAsync(screenshotPath);
                    Console.WriteLine($"✅ Capture d'écran sauvegardée : {screenshotPath}");

                    return "";
                });

                await amazonLoader.CloseBrowser();
                Console.WriteLine();
                Console.WriteLine("✅ Diagnostic terminé avec succès");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur durant le diagnostic : {ex.Message}");
                Console.WriteLine($"Stack trace : {ex.StackTrace}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Test spécifique de l'extracteur de produits
        /// </summary>
        public static async Task TestProductExtractor()
        {
            Console.WriteLine("🧪 TEST DE L'EXTRACTEUR DE PRODUITS");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AmazonDealsExtractor(amazonLoader);

            try
            {
                await amazonLoader.StartBrowser(headless: false);
                Console.WriteLine("✅ Navigateur démarré");

                Console.WriteLine("🔍 Extraction des produits (1 page)...");
                var products = await extractor.ExtractAllDealsProducts(maxPages: 1);
                
                Console.WriteLine($"📊 Résultats :");
                Console.WriteLine($"   Produits extraits : {products.Count}");
                
                if (products.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("📋 Détails des premiers produits :");
                    for (int i = 0; i < Math.Min(products.Count, 5); i++)
                    {
                        var product = products[i];
                        Console.WriteLine($"   {i + 1}. {product.Title}");
                        Console.WriteLine($"      URL: {product.ProductUrl}");
                        Console.WriteLine($"      Prix: {product.Price}");
                        Console.WriteLine($"      Prix original: {product.OriginalPrice}");
                        Console.WriteLine($"      Réduction: {product.Discount}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("❌ Aucun produit extrait - Problème détecté !");
                    Console.WriteLine();
                    Console.WriteLine("🔧 Lancement du diagnostic avancé...");
                    await DiagnoseAdvancedPageStructure(amazonLoader);
                }

                await amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur : {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Diagnostic avancé de la structure de la page Amazon
        /// </summary>
        private static async Task DiagnoseAdvancedPageStructure(AmazonLoader<string> amazonLoader)
        {
            var dealsUrl = "https://www.amazon.fr/deals?ref_=nav_cs_gb";

            await amazonLoader.LoadPage(dealsUrl, async (page) =>
            {
                Console.WriteLine("🔍 Diagnostic avancé de la structure de la page...");

                // Attendre que la page se charge complètement
                await Task.Delay(5000);

                var diagnosticInfo = await page.EvaluateFunctionAsync<string>(@"() => {
                    const results = [];

                    // 1. Informations générales
                    results.push('=== INFORMATIONS GÉNÉRALES ===');
                    results.push(`URL: ${window.location.href}`);
                    results.push(`Titre: ${document.title}`);
                    results.push(`Total éléments DOM: ${document.querySelectorAll('*').length}`);
                    results.push('');

                    // 2. Recherche de conteneurs de produits
                    results.push('=== CONTENEURS DE PRODUITS ===');
                    const containerSelectors = [
                        '[data-csa-c-type=""widget""]',
                        '[data-csa-c-slot-id]',
                        '.a-carousel-card',
                        '.p13n-desktop-carousel-cards',
                        '[data-asin]',
                        '.s-result-item',
                        '[data-deal-id]'
                    ];

                    containerSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        results.push(`${selector}: ${elements.length} éléments`);
                        if (elements.length > 0) {
                            results.push(`  Premier élément: ${elements[0].tagName} - Classes: ${elements[0].className}`);
                        }
                    });
                    results.push('');

                    // 3. Recherche de liens produits
                    results.push('=== LIENS PRODUITS ===');
                    const productLinks = document.querySelectorAll('a[href*=""/dp/""], a[href*=""/gp/product/""]');
                    results.push(`Liens produits (/dp/ ou /gp/product/): ${productLinks.length}`);

                    if (productLinks.length > 0) {
                        results.push('Exemples de liens:');
                        for (let i = 0; i < Math.min(5, productLinks.length); i++) {
                            const link = productLinks[i];
                            results.push(`  ${i+1}. ${link.href}`);
                            results.push(`     Texte: ${link.textContent.trim().substring(0, 50)}...`);
                        }
                    }
                    results.push('');

                    // 4. Recherche de structures spécifiques Amazon 2025
                    results.push('=== STRUCTURES AMAZON 2025 ===');
                    const modernSelectors = [
                        '[data-csa-c-painter]',
                        '[data-csa-c-content-id]',
                        '.hve-deals-DESKTOP-slot',
                        '[id*=""slot""]',
                        '[class*=""carousel""]',
                        '[class*=""widget""]'
                    ];

                    modernSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        results.push(`${selector}: ${elements.length} éléments`);
                    });
                    results.push('');

                    // 5. Vérification des erreurs
                    results.push('=== VÉRIFICATIONS D'ERREURS ===');
                    const errorMessages = document.querySelectorAll('.a-alert-error, .error-message, [data-testid=""error""]');
                    if (errorMessages.length > 0) {
                        results.push(`Messages d'erreur détectés: ${errorMessages.length}`);
                    }

                    const captcha = document.querySelector('form[action*=""captcha""], #captchacharacters');
                    if (captcha) {
                        results.push('CAPTCHA détecté sur la page !');
                    }

                    // 6. Analyse du contenu dynamique
                    results.push('');
                    results.push('=== CONTENU DYNAMIQUE ===');
                    const scripts = document.querySelectorAll('script');
                    let hasReactOrVue = false;
                    scripts.forEach(script => {
                        if (script.textContent.includes('React') || script.textContent.includes('Vue')) {
                            hasReactOrVue = true;
                        }
                    });
                    results.push(`Scripts React/Vue détectés: ${hasReactOrVue}`);

                    return results.join('\n');
                }");

                Console.WriteLine(diagnosticInfo);

                // Sauvegarder le HTML pour analyse
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var htmlContent = await page.GetContentAsync();
                var htmlPath = $"amazon_deals_debug_{timestamp}.html";
                await File.WriteAllTextAsync(htmlPath, htmlContent);
                Console.WriteLine($"📄 HTML sauvegardé: {htmlPath}");

                // Prendre une capture d'écran
                var screenshotPath = $"amazon_deals_screenshot_{timestamp}.png";
                await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                {
                    FullPage = true
                });
                Console.WriteLine($"📸 Capture d'écran: {screenshotPath}");

                return "";
            });
        }

        /// <summary>
        /// Test de navigation et de pagination
        /// </summary>
        public static async Task TestPagination()
        {
            Console.WriteLine("📄 TEST DE PAGINATION");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();

            try
            {
                await amazonLoader.StartBrowser(headless: false);
                
                await amazonLoader.LoadPage("https://www.amazon.fr/deals?ref_=nav_cs_gb", async (page) =>
                {
                    Console.WriteLine("🔍 Recherche des boutons de pagination...");
                    
                    var paginationInfo = await page.EvaluateFunctionAsync<string>(@"() => {
                        const results = [];
                        
                        const nextSelectors = [
                            'a[aria-label=""Aller à la page suivante""]',
                            'a[aria-label=""Go to next page""]',
                            '.a-pagination .a-last a',
                            'a[aria-label*=""suivant""]',
                            'a[aria-label*=""next""]',
                            '.a-pagination li:last-child a:not(.a-disabled)'
                        ];
                        
                        for (const selector of nextSelectors) {
                            const elements = document.querySelectorAll(selector);
                            results.push(`${selector}: ${elements.length} éléments`);
                            
                            if (elements.length > 0) {
                                const first = elements[0];
                                results.push(`  Texte: ${first.textContent.trim()}`);
                                results.push(`  Href: ${first.href || 'Pas de href'}`);
                                results.push(`  Disabled: ${first.classList.contains('a-disabled')}`);
                            }
                        }
                        
                        return results.join('\n');
                    }");

                    Console.WriteLine("Résultats de la pagination :");
                    Console.WriteLine(paginationInfo);

                    return "";
                });

                await amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur : {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Génère un rapport complet de diagnostic
        /// </summary>
        public static async Task GenerateFullDiagnosticReport()
        {
            var reportPath = $"scraping_diagnostic_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            var report = new List<string>();

            report.Add("RAPPORT DE DIAGNOSTIC DU SCRAPING AMAZON");
            report.Add("=" + new string('=', 50));
            report.Add($"Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.Add("");

            Console.WriteLine("📋 Génération du rapport de diagnostic complet...");

            try
            {
                // Rediriger la sortie console vers le rapport
                var originalOut = Console.Out;
                using var stringWriter = new StringWriter();
                Console.SetOut(stringWriter);

                await DiagnoseScrapingIssues();
                
                Console.SetOut(originalOut);
                report.Add("DIAGNOSTIC PRINCIPAL:");
                report.Add(stringWriter.ToString());

                await File.WriteAllLinesAsync(reportPath, report);
                Console.WriteLine($"✅ Rapport de diagnostic sauvegardé : {reportPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la génération du rapport : {ex.Message}");
            }
        }
    }
}
