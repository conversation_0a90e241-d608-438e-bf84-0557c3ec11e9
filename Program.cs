﻿using System.Threading.Tasks;

namespace Amazon2FacebookPoster
{
    internal class Program
    {
        private static async Task Main(string[] args)
        {
            Console.WriteLine("🛒 Amazon2FacebookPoster - Optimized Edition");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine("🚀 Choose your experience:");
            Console.WriteLine("1. 🆕 New Optimized Interface (Recommended)");
            Console.WriteLine("2. 📜 Legacy Interface");
            Console.WriteLine("3. 🧪 Run Test Suite");
            Console.WriteLine();
            Console.Write("Choose option (1-3): ");

            var choice = Console.ReadLine()?.Trim();

            switch (choice)
            {
                case "1":
                    await OptimizedProgram.Main(args);
                    break;
                case "2":
                    await RunLegacyInterface();
                    break;
                case "3":
                    await RunTestSuite();
                    break;
                default:
                    Console.WriteLine("Invalid choice. Running optimized interface...");
                    await OptimizedProgram.Main(args);
                    break;
            }
        }

        private static async Task RunTestSuite()
        {
            Console.Clear();
            Console.WriteLine("🧪 TEST SUITE");
            Console.WriteLine("=" + new string('=', 15));
            Console.WriteLine();

            Console.Write("Enter Gemini API key for testing: ");
            var apiKey = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(apiKey))
            {
                Console.WriteLine("❌ API key required for testing");
                return;
            }

            var testSuite = new OptimizedTestSuite(apiKey);
            var result = await testSuite.RunAllTestsAsync();

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static async Task RunLegacyInterface()
        {
            Console.Clear();
            Console.WriteLine("📜 LEGACY INTERFACE");
            Console.WriteLine("=" + new string('=', 20));
            Console.WriteLine();

            // Configuration de la clé API Gemini
            Console.Write("Enter your Gemini API key: ");
            var geminiApiKey = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(geminiApiKey))
            {
                Console.WriteLine("❌ Gemini API key is required for legacy interface");
                Console.WriteLine("Get your key from: https://makersuite.google.com/app/apikey");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return;
            }

            // Menu principal simplifié
            while (true)
            {
                Console.Clear();
                Console.WriteLine("🛒 Amazon2FacebookPoster - Menu Principal");
                Console.WriteLine("=" + new string('=', 50));
                Console.WriteLine();
                Console.WriteLine("🚀 PROCESSUS PRINCIPAL :");
                Console.WriteLine("1. 🚀 Processus COMPLET IA (Scraping + Détails + Affiliation + Posts)");
                Console.WriteLine("2. ⚡ Scraping DIRECT optimisé (CSS + fallback IA optionnel)");
                Console.WriteLine();
                Console.WriteLine("🔧 OPTIONS SUPPLÉMENTAIRES :");
                Console.WriteLine("3. 🧪 Test rapide (mode debug)");
                Console.WriteLine("4. 📂 Générer posts depuis JSON existant");
                Console.WriteLine("5. ❌ Quitter");
                Console.WriteLine();
                Console.WriteLine("💡 Nouveau : Option 2 pour scraping direct avec sélecteurs CSS optimisés");
                Console.WriteLine("💡 Recommandé : Option 1 pour le processus complet IA");
                Console.WriteLine();
                Console.Write("Choisissez une option (1-5): ");

                var choice = Console.ReadLine();

                try
                {
                    switch (choice)
                    {
                        case "1":
                            await RunCompleteAiProcess(geminiApiKey);
                            break;
                        case "2":
                            await RunDirectScrapingProcess(geminiApiKey);
                            break;
                        case "3":
                            await RunQuickTest(geminiApiKey);
                            break;
                        case "4":
                            await RunFromJson(geminiApiKey);
                            break;
                        case "5":
                            Console.WriteLine("👋 Au revoir !");
                            return;
                        default:
                            Console.WriteLine("❌ Option invalide. Appuyez sur une touche pour continuer...");
                            Console.ReadKey();
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur: {ex.Message}");
                    Console.WriteLine("Appuyez sur une touche pour continuer...");
                    Console.ReadKey();
                }
            }
        }



        private static async Task RunDirectScrapingProcess(string geminiApiKey)
        {
            Console.Clear();
            Console.WriteLine("⚡ SCRAPING DIRECT OPTIMISÉ");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();
            Console.WriteLine("🎯 SCRAPING DIRECT AVEC SÉLECTEURS CSS :");
            Console.WriteLine("• Utilise les sélecteurs CSS spécifiques identifiés");
            Console.WriteLine("• div[class*=\"ProductCard-module__card\"][data-asin]");
            Console.WriteLine("• a[data-testid=\"product-card-link\"]");
            Console.WriteLine("• div[class*=\"style_badgeContainer\"] pour les réductions");
            Console.WriteLine("• Fallback IA optionnel si aucun produit trouvé");
            Console.WriteLine("• Plus rapide et plus fiable que le scraping IA pur");
            Console.WriteLine();

            Console.Write("Nombre de pages à traiter (recommandé: 3-5, max: 10): ");
            var maxPagesInput = Console.ReadLine();
            int maxPages = int.TryParse(maxPagesInput, out var pages) ? Math.Min(pages, 10) : 3;

            Console.Write("Votre tag Amazon Associates (ex: votre-tag-20): ");
            var associateTag = Console.ReadLine();
            if (string.IsNullOrEmpty(associateTag))
            {
                associateTag = "direct-tag-20";
                Console.WriteLine($"⚠️ Tag par défaut utilisé: {associateTag}");
            }

            Console.Write("Activer le fallback IA si scraping direct échoue ? (y/n, recommandé: y): ");
            var aiFallbackInput = Console.ReadLine();
            bool useAiFallback = aiFallbackInput?.ToLower() != "n";

            Console.Write("Mode headless ? (y/n, recommandé: n pour voir le processus): ");
            var headlessInput = Console.ReadLine();
            bool headless = headlessInput?.ToLower() == "y";

            Console.WriteLine();
            Console.WriteLine("⚡ DÉMARRAGE DU SCRAPING DIRECT...");
            Console.WriteLine($"   📄 Pages à traiter: {maxPages}");
            Console.WriteLine($"   🏷️ Tag d'affiliation: {associateTag}");
            Console.WriteLine($"   🤖 Fallback IA: {(useAiFallback ? "Activé" : "Désactivé")}");
            Console.WriteLine($"   👁️ Mode headless: {headless}");
            Console.WriteLine();

            Console.Write("Générer les posts Facebook directement ? (y/n, recommandé: y): ");
            var generatePostsInput = Console.ReadLine();
            bool generatePosts = generatePostsInput?.ToLower() != "n";

            try
            {
                // Créer les options de traitement
                var options = new ProcessingOptions
                {
                    MaxPages = maxPages,
                    AmazonAssociateTag = associateTag,
                    HeadlessMode = headless,
                    OutputDirectory = $"direct_scraping_{DateTime.Now:yyyyMMdd_HHmmss}",
                    GeneratePosts = generatePosts,
                    SaveProductsJson = true,
                    DelayBetweenPosts = 2000
                };

                // Créer le processeur direct
                var processor = new DirectScrapingProcessor(options, geminiApiKey);

                // Lancer le processus complet
                await processor.ProcessCompleteWorkflow(useAiFallback);

                Console.WriteLine();
                Console.WriteLine("🎉 PROCESSUS SCRAPING DIRECT TERMINÉ AVEC SUCCÈS !");
                Console.WriteLine($"   📁 Dossier de sortie: {options.OutputDirectory}");

                if (generatePosts)
                {
                    Console.WriteLine("   📝 Posts Facebook générés dans le dossier de sortie");
                }
                else
                {
                    Console.WriteLine("   💾 Fichier JSON généré - utilisez l'option 4 du menu pour créer les posts");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du scraping direct : {ex.Message}");
                Console.WriteLine("💡 Conseils de dépannage :");
                Console.WriteLine("   • Vérifiez votre connexion internet");
                Console.WriteLine("   • Essayez en mode non-headless pour voir les erreurs");
                Console.WriteLine("   • Activez le fallback IA si les sélecteurs CSS ont changé");
            }

            Console.WriteLine();
            Console.WriteLine("Appuyez sur une touche pour revenir au menu...");
            Console.ReadKey();
        }

        private static async Task RunQuickTest(string geminiApiKey)
        {
            Console.Clear();
            Console.WriteLine("🧪 TEST RAPIDE - Une page, 3 premiers produits");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();

            Console.Write("Votre tag Amazon Associates (ex: votre-tag-20): ");
            var associateTag = Console.ReadLine() ?? "test-tag-20";

            var options = new ProcessingOptions
            {
                MaxPages = 1,
                AmazonAssociateTag = associateTag,
                HeadlessMode = false, // Mode visible pour le test
                OutputDirectory = "facebook_posts_test",
                GeneratePosts = true,
                SaveProductsJson = true
            };

            var processor = new AmazonDealsProcessor(options);
            processor.SetGeminiApiKey(geminiApiKey);

            Console.WriteLine();
            Console.WriteLine("🚀 Démarrage du test...");

            await processor.ProcessSinglePage();

            Console.WriteLine();
            Console.WriteLine("✅ Test terminé ! Vérifiez le dossier 'facebook_posts_test'");
            Console.WriteLine("Appuyez sur une touche pour revenir au menu...");
            Console.ReadKey();
        }



        private static async Task RunFromJson(string geminiApiKey)
        {
            Console.Clear();
            Console.WriteLine("📂 GÉNÉRATION DEPUIS JSON");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine();

            Console.Write("Chemin vers le fichier JSON (ex: preview_products.json): ");
            var jsonPath = Console.ReadLine();

            if (string.IsNullOrEmpty(jsonPath) || !File.Exists(jsonPath))
            {
                Console.WriteLine("❌ Fichier non trouvé !");
                Console.WriteLine("Appuyez sur une touche pour revenir au menu...");
                Console.ReadKey();
                return;
            }

            var options = new ProcessingOptions
            {
                OutputDirectory = "facebook_posts_from_json",
                GeneratePosts = true,
                DelayBetweenPosts = 1500
            };

            var processor = new AmazonDealsProcessor(options);
            processor.SetGeminiApiKey(geminiApiKey);

            Console.WriteLine();
            Console.WriteLine("🚀 Génération des posts depuis le JSON...");

            await processor.ProcessFromJson(jsonPath);

            Console.WriteLine();
            Console.WriteLine("✅ Génération terminée ! Vérifiez le dossier 'facebook_posts_from_json'");
            Console.WriteLine("Appuyez sur une touche pour revenir au menu...");
            Console.ReadKey();
        }

        private static async Task RunCompleteAiProcess(string geminiApiKey)
        {
            Console.Clear();
            Console.WriteLine("🚀 PROCESSUS COMPLET IA OPTIMISÉ");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();
            Console.WriteLine("🎯 PROCESSUS AUTOMATISÉ COMPLET :");
            Console.WriteLine("1. 📋 Scraping IA optimisé (TOUS les produits détectés)");
            Console.WriteLine("2. 🔍 Extraction complète des détails produits");
            Console.WriteLine("3. 🔗 Génération automatique des liens d'affiliation");
            Console.WriteLine("4. 📝 Création des posts Facebook optimisés");
            Console.WriteLine();
            Console.WriteLine("🚀 OPTIMISATIONS GOOGLE GEMINI (1M+ TOKENS) :");
            Console.WriteLine("• Détection ULTRA-EXHAUSTIVE (limite 4M caractères HTML)");
            Console.WriteLine("• Contexte étendu par produit (3000 caractères vs 1200)");
            Console.WriteLine("• 20 clics 'Afficher plus' automatiques (vs 10)");
            Console.WriteLine("• Analyse IA renforcée avec prompts optimisés");
            Console.WriteLine("• Délais humains simulés pour éviter la détection");
            Console.WriteLine();

            Console.Write("Nombre de pages à traiter (recommandé: 3-5, max: 10): ");
            var maxPagesInput = Console.ReadLine();
            int maxPages = int.TryParse(maxPagesInput, out var pages) ? Math.Min(pages, 10) : 3;

            Console.Write("Votre tag Amazon Associates (ex: votre-tag-20): ");
            var associateTag = Console.ReadLine();
            if (string.IsNullOrEmpty(associateTag))
            {
                associateTag = "complete-tag-20";
                Console.WriteLine($"⚠️ Tag par défaut utilisé: {associateTag}");
            }

            Console.Write("Mode headless ? (y/n, recommandé: n pour voir le processus): ");
            var headlessInput = Console.ReadLine();
            bool headless = headlessInput?.ToLower() == "y";

            Console.WriteLine();
            Console.WriteLine("🚀 DÉMARRAGE DU PROCESSUS OPTIMISÉ...");
            Console.WriteLine($"   📄 Pages à traiter: {maxPages}");
            Console.WriteLine($"   🏷️ Tag d'affiliation: {associateTag}");
            Console.WriteLine($"   👁️ Mode headless: {headless}");
            Console.WriteLine($"   🔄 Clics 'Afficher plus': 20 max par page (Gemini optimisé)");
            Console.WriteLine();

            try
            {
                var processor = new CompleteAiProcessor(geminiApiKey, associateTag);
                var result = await processor.ProcessCompleteWorkflow(maxPages, headless);

                Console.WriteLine();
                if (result.Success)
                {
                    Console.WriteLine("🎉 PROCESSUS COMPLET RÉUSSI !");
                    Console.WriteLine($"   📊 Produits trouvés: {result.ProductsFound}");
                    Console.WriteLine($"   📝 Posts générés: {result.PostsGenerated}");
                    Console.WriteLine($"   📈 Taux de réussite: {(result.PostsGenerated * 100 / Math.Max(1, result.ProductsFound)):F1}%");
                    Console.WriteLine();
                    Console.WriteLine("📁 Vérifiez les dossiers générés pour les résultats :");
                    Console.WriteLine("   • deals_list_[timestamp].json - Liste initiale des produits");
                    Console.WriteLine("   • complete_products_[timestamp].json - Produits avec détails complets");
                    Console.WriteLine("   • facebook_posts_[timestamp]/ - Posts Facebook générés");
                }
                else
                {
                    Console.WriteLine("❌ ERREUR DANS LE PROCESSUS");
                    Console.WriteLine($"   Erreur: {result.Error}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du processus complet : {ex.Message}");
                Console.WriteLine("💡 Conseils de dépannage :");
                Console.WriteLine("   • Vérifiez votre connexion internet");
                Console.WriteLine("   • Vérifiez que la clé API Gemini est valide");
                Console.WriteLine("   • Essayez en mode non-headless pour voir les erreurs");
            }

            Console.WriteLine();
            Console.WriteLine("Appuyez sur une touche pour revenir au menu...");
            Console.ReadKey();
        }


    }
}