# Centralized Configuration System for Extraction Rules

## 🎯 Overview

The centralized configuration system allows complete customization of Amazon product extraction without modifying code. This enables non-technical users to update extraction rules when Amazon changes their HTML structure, simply by editing JSON configuration files.

## 🏗️ Architecture

### **Core Components**

1. **`extraction-rules.json`** - Main configuration file with extraction rules
2. **`ExtractionConfigurationManager`** - Loads, validates, and hot-reloads configuration
3. **`ConfigurableExtractionEngine`** - Executes extraction using configuration rules
4. **`ConfigurableExtractionStrategy`** - Integration with existing extraction system
5. **`ConfigurationManagementTools`** - Tools for testing and managing configurations

### **Configuration Structure**

```json
{
  "version": "1.0.0",
  "regions": {
    "amazon.fr": {
      "name": "Amazon France",
      "baseUrl": "https://www.amazon.fr",
      "currency": "EUR",
      "dealsUrl": "https://www.amazon.fr/deals"
    }
  },
  "extractionRules": {
    "productList": {
      "containerSelectors": { /* Product container selectors */ },
      "fields": { /* Field extraction rules */ }
    },
    "productPage": {
      "containerSelectors": { /* Individual product page selectors */ },
      "fields": { /* Detailed field extraction rules */ }
    }
  },
  "transformations": { /* Data transformation functions */ },
  "validation": { /* Validation rules */ }
}
```

## 🔧 Configuration Features

### **1. Dynamic Class Name Handling**

Supports Amazon's dynamic class names with wildcard matching:

```json
{
  "selector": "div[class*='ProductCard-module__card'][data-asin]",
  "priority": 1,
  "description": "Dynamic product card with ASIN"
}
```

### **2. Multi-Level Fallback System**

Configurable selector hierarchies with priority ordering:

```json
{
  "selectors": [
    {
      "selector": ".a-truncate-full",
      "priority": 1,
      "description": "Primary title selector"
    },
    {
      "selector": "h3 a span",
      "priority": 2,
      "description": "Fallback title selector"
    }
  ]
}
```

### **3. Data Transformations**

Configurable data processing functions:

```json
{
  "transformations": {
    "extractDiscount": {
      "description": "Extract discount percentage or amount",
      "function": "text => { const match = text.match(/(\\d+)\\s*(%|€)/); return match ? `-${match[1]}${match[2]}` : text; }"
    }
  }
}
```

### **4. Field Validation**

Comprehensive validation rules for extracted data:

```json
{
  "validation": {
    "required": true,
    "minLength": 10,
    "pattern": "^[A-Z0-9]{10}$",
    "mustContain": ["/dp/", "amazon."]
  }
}
```

## 🚀 Usage Examples

### **1. Basic Configurable Extraction**

```csharp
// Use configurable strategy
var config = new AmazonScrapingConfiguration
{
    ExtractionStrategy = ExtractionStrategy.Configurable,
    MaxPages = 5
};

using var processor = new UnifiedAmazonProcessor(config);
var result = await processor.ProcessAsync();
```

### **2. Configuration Management**

```csharp
// Access configuration tools
var tools = new ConfigurationManagementTools();
await tools.RunConfigurationEditorAsync();
```

### **3. Hot-Reload Configuration**

```csharp
// Configuration automatically reloads when file changes
var configManager = new ExtractionConfigurationManager("extraction-rules.json");
configManager.ConfigurationChanged += (sender, e) => {
    Console.WriteLine($"Configuration updated to version {e.NewConfiguration.Version}");
};
```

## 🛠️ Configuration Management Tools

### **Interactive Configuration Editor**

Access via the main menu → Option 8: Configuration Management Tools

**Features:**
- 📖 View current configuration
- ✅ Validate configuration syntax and rules
- 🔄 Reload configuration from file
- 🎯 Test selectors on live Amazon pages
- 🔍 Test single product extraction
- 📝 Create default configuration

### **Live Selector Testing**

Test your selectors against real Amazon pages:

```bash
# Run the application
dotnet run

# Choose: 8. Configuration Management Tools
# Then: 5. Test Selectors on Live Page
# Enter URL or use default Amazon deals page
```

### **Configuration Validation**

Comprehensive validation ensures configuration integrity:

- **Syntax validation** - JSON structure and schema compliance
- **Selector validation** - CSS selector syntax checking
- **Logic validation** - Priority ordering and fallback rules
- **Data validation** - Field requirements and patterns

## 📊 Supported Extraction Targets

### **Product List Extraction (Deals Page)**

| Field | Description | Selectors | Fallback |
|-------|-------------|-----------|----------|
| **title** | Product title | `.a-truncate-full`, `h3 a span` | Multiple fallbacks |
| **url** | Product URL | `a[data-testid="product-card-link"]` | Generate from ASIN |
| **asin** | Amazon ID | `[data-asin]` | Extract from URL |
| **price** | Current price | `.a-price .a-offscreen` | Multiple price selectors |
| **discount** | Discount info | `div[class*="style_badgeContainer"]` | Badge extraction |
| **image** | Product image | `img[src*="images-amazon"]` | Multiple image sources |
| **rating** | Product rating | `[class*="rating"] span` | Rating extraction |

### **Product Page Extraction (Individual Products)**

- **Detailed product information**
- **Affiliate link extraction** (`'Obtenir un lien'` buttons)
- **Commission rate detection**
- **Enhanced product details**
- **Review and rating information**

## 🌍 Multi-Region Support

Configure different Amazon marketplaces:

```json
{
  "regions": {
    "amazon.fr": {
      "name": "Amazon France",
      "baseUrl": "https://www.amazon.fr",
      "currency": "EUR",
      "language": "fr"
    },
    "amazon.com": {
      "name": "Amazon US",
      "baseUrl": "https://www.amazon.com",
      "currency": "USD",
      "language": "en"
    }
  }
}
```

## 🔄 Hot-Reload Configuration

The system supports real-time configuration updates:

1. **File Monitoring** - Automatically detects changes to `extraction-rules.json`
2. **Validation** - Validates new configuration before applying
3. **Event Notification** - Notifies components of configuration changes
4. **Graceful Updates** - Applies changes without restart

## 📝 Creating Custom Configurations

### **Step 1: Copy Default Configuration**

```bash
# Run configuration tools
dotnet run
# Choose: 8. Configuration Management Tools
# Then: 4. Create Default Configuration
```

### **Step 2: Customize Selectors**

Edit `extraction-rules.json` to add your selectors:

```json
{
  "fields": {
    "customField": {
      "description": "Custom field extraction",
      "selectors": [
        {
          "selector": ".your-custom-selector",
          "priority": 1,
          "extraction": "textContent",
          "transform": "trim"
        }
      ]
    }
  }
}
```

### **Step 3: Test Configuration**

Use the testing tools to validate your changes:

```bash
# Test selectors on live page
# Choose: 5. Test Selectors on Live Page
```

### **Step 4: Deploy Configuration**

The configuration is automatically reloaded when saved.

## 🛡️ Error Handling & Validation

### **Configuration Validation**

- **Schema validation** against JSON schema
- **Selector syntax** validation
- **Priority ordering** validation
- **Required field** validation

### **Runtime Error Handling**

- **Graceful degradation** when selectors fail
- **Fallback strategies** for missing data
- **Comprehensive logging** for debugging
- **Automatic retry** mechanisms

## 🎯 Best Practices

### **1. Selector Design**

- **Use data attributes** when available (most stable)
- **Implement multiple fallbacks** for each field
- **Order by reliability** (priority 1 = most reliable)
- **Test on live pages** before deployment

### **2. Configuration Management**

- **Version your configurations** using semantic versioning
- **Document changes** in the description field
- **Test thoroughly** before production deployment
- **Keep backups** of working configurations

### **3. Performance Optimization**

- **Minimize selector complexity** for better performance
- **Use specific selectors** to reduce DOM traversal
- **Implement efficient fallbacks** to avoid unnecessary processing
- **Monitor extraction success rates** to detect issues

## 🔮 Future Enhancements

### **Planned Features**

- **Visual selector builder** - GUI for creating selectors
- **A/B testing framework** - Test multiple configurations
- **Performance analytics** - Track extraction success rates
- **Configuration templates** - Pre-built configurations for common scenarios
- **Automated selector discovery** - AI-powered selector generation

### **Extensibility**

The system is designed for easy extension:

- **Custom transformation functions**
- **Additional validation rules**
- **New extraction strategies**
- **Enhanced testing tools**

## 📚 Examples & Templates

### **Basic Product List Configuration**

See `extraction-rules.json` for a complete example configuration.

### **Custom Transformation Example**

```json
{
  "transformations": {
    "customPriceFormat": {
      "description": "Format price with currency symbol",
      "function": "price => price.replace(/[^0-9,.]/, '') + ' €'"
    }
  }
}
```

### **Advanced Validation Example**

```json
{
  "validation": {
    "title": {
      "required": true,
      "minLength": 10,
      "maxLength": 200,
      "mustContain": ["product", "item"]
    }
  }
}
```

This centralized configuration system provides complete flexibility for managing Amazon extraction rules without code modifications, making it perfect for non-technical users and dynamic environments where Amazon frequently changes their HTML structure.
