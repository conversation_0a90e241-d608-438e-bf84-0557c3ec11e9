using PuppeteerSharp;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Configurable extraction engine that uses configuration rules instead of hardcoded selectors
    /// </summary>
    public class ConfigurableExtractionEngine
    {
        private readonly ExtractionConfigurationManager _configManager;
        private readonly string _region;
        private ExtractionConfiguration? _currentConfig;

        public ConfigurableExtractionEngine(ExtractionConfigurationManager configManager, string region = "amazon.fr")
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _region = region;
            
            // Subscribe to configuration changes
            _configManager.ConfigurationChanged += OnConfigurationChanged;
        }

        /// <summary>
        /// Extract products from a page using configured rules
        /// </summary>
        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, string pageType = "productList")
        {
            var config = await EnsureConfigurationLoadedAsync();
            var ruleSet = GetRuleSetForPageType(config, pageType);
            
            if (ruleSet == null)
            {
                throw new InvalidOperationException($"No extraction rules found for page type: {pageType}");
            }

            Logger.LogInfo($"Starting configurable extraction for {pageType} using {ruleSet.ContainerSelectors.Selectors.Count} container selectors", "ConfigurableExtraction");

            return await page.EvaluateFunctionAsync<List<ProductInfo>>($@"
                (function() {{
                    const config = {JsonSerializer.Serialize(ruleSet)};
                    const regionConfig = {JsonSerializer.Serialize(config.Regions[_region])};
                    const transformations = {JsonSerializer.Serialize(config.Transformations)};
                    
                    {GenerateExtractionJavaScript()}
                    
                    return extractProducts(config, regionConfig, transformations);
                }})()
            ");
        }

        /// <summary>
        /// Extract data from a single product container using configured rules
        /// </summary>
        public async Task<ProductInfo?> ExtractSingleProductAsync(IPage page, string containerSelector, string pageType = "productPage")
        {
            var config = await EnsureConfigurationLoadedAsync();
            var ruleSet = GetRuleSetForPageType(config, pageType);
            
            if (ruleSet == null)
            {
                return null;
            }

            return await page.EvaluateFunctionAsync<ProductInfo?>($@"
                (function() {{
                    const config = {JsonSerializer.Serialize(ruleSet)};
                    const regionConfig = {JsonSerializer.Serialize(config.Regions[_region])};
                    const transformations = {JsonSerializer.Serialize(config.Transformations)};
                    const container = document.querySelector('{containerSelector}');
                    
                    if (!container) return null;
                    
                    {GenerateExtractionJavaScript()}
                    
                    return extractSingleProduct(container, config, regionConfig, transformations);
                }})()
            ");
        }

        /// <summary>
        /// Test selectors against current page
        /// </summary>
        public async Task<SelectorTestResult> TestSelectorsAsync(IPage page, string pageType = "productList")
        {
            var config = await EnsureConfigurationLoadedAsync();
            var ruleSet = GetRuleSetForPageType(config, pageType);
            var result = new SelectorTestResult();

            if (ruleSet == null)
            {
                result.AddError($"No rule set found for page type: {pageType}");
                return result;
            }

            try
            {
                // Test container selectors
                foreach (var selector in ruleSet.ContainerSelectors.Selectors.OrderBy(s => s.Priority))
                {
                    var count = await page.EvaluateFunctionAsync<int>($@"
                        document.querySelectorAll('{selector.Selector}').length
                    ");

                    if (count > 0)
                    {
                        result.AddInfo($"✅ Container selector '{selector.Selector}' found {count} elements");
                    }
                    else
                    {
                        result.AddWarning($"⚠️ Container selector '{selector.Selector}' found 0 elements");
                    }
                }

                // Test field selectors
                foreach (var (fieldName, field) in ruleSet.Fields)
                {
                    var fieldResults = new List<string>();
                    
                    foreach (var selector in field.Selectors.OrderBy(s => s.Priority))
                    {
                        var count = await page.EvaluateFunctionAsync<int>($@"
                            document.querySelectorAll('{selector.Selector}').length
                        ");

                        fieldResults.Add($"{selector.Selector}: {count} elements");
                    }

                    result.AddInfo($"Field '{fieldName}': {string.Join(", ", fieldResults)}");
                }

                Logger.LogInfo($"Selector test completed for {pageType}. Found {result.Info.Count} results", "ConfigurableExtraction");
            }
            catch (Exception ex)
            {
                result.AddError($"Selector test failed: {ex.Message}");
                Logger.LogError($"Selector test failed: {ex.Message}", ex, "ConfigurableExtraction");
            }

            return result;
        }

        /// <summary>
        /// Validate extracted data against configuration rules
        /// </summary>
        public ValidationResult ValidateExtractedData(ProductInfo product, string pageType = "productList")
        {
            var config = _currentConfig;
            if (config == null)
            {
                return new ValidationResult { IsValid = false, Errors = { "Configuration not loaded" } };
            }

            var result = new ValidationResult();
            var ruleSet = GetRuleSetForPageType(config, pageType);
            
            if (ruleSet == null)
            {
                result.AddError($"No validation rules for page type: {pageType}");
                return result;
            }

            // Validate each field
            foreach (var (fieldName, fieldConfig) in ruleSet.Fields)
            {
                if (fieldConfig.Validation == null) continue;

                var fieldValue = GetFieldValue(product, fieldName);
                ValidateField(fieldName, fieldValue, fieldConfig.Validation, result);
            }

            return result;
        }

        /// <summary>
        /// Generate JavaScript extraction code based on configuration
        /// </summary>
        private string GenerateExtractionJavaScript()
        {
            return @"
                function extractProducts(config, regionConfig, transformations) {
                    const products = [];
                    
                    // Find containers using configured selectors
                    let containers = [];
                    for (const containerSelector of config.containerSelectors.selectors.sort((a, b) => a.priority - b.priority)) {
                        try {
                            containers = document.querySelectorAll(containerSelector.selector);
                            if (containers.length > 0) {
                                console.log(`Found ${containers.length} containers using: ${containerSelector.selector}`);
                                break;
                            }
                        } catch (e) {
                            console.warn(`Container selector failed: ${containerSelector.selector}`, e);
                        }
                    }
                    
                    // Extract data from each container
                    containers.forEach((container, index) => {
                        try {
                            const product = extractSingleProduct(container, config, regionConfig, transformations);
                            if (product && isValidProduct(product, config)) {
                                products.push(product);
                            }
                        } catch (e) {
                            console.warn(`Failed to extract product ${index + 1}:`, e);
                        }
                    });
                    
                    console.log(`Extracted ${products.length} valid products`);
                    return products;
                }
                
                function extractSingleProduct(container, config, regionConfig, transformations) {
                    const product = {
                        extractedAt: new Date().toISOString()
                    };
                    
                    // Extract each configured field
                    for (const [fieldName, fieldConfig] of Object.entries(config.fields)) {
                        const value = extractField(container, fieldConfig, regionConfig, transformations);
                        if (value !== null && value !== undefined && value !== '') {
                            product[fieldName] = value;
                        }
                    }
                    
                    return product;
                }
                
                function extractField(container, fieldConfig, regionConfig, transformations) {
                    // Try selectors in priority order
                    for (const selectorConfig of fieldConfig.selectors.sort((a, b) => a.priority - b.priority)) {
                        try {
                            const element = container.querySelector(selectorConfig.selector);
                            if (element) {
                                let value = extractValueFromElement(element, selectorConfig.extraction || 'textContent');
                                
                                // Apply transformation if specified
                                if (selectorConfig.transform && transformations[selectorConfig.transform]) {
                                    value = applyTransformation(value, transformations[selectorConfig.transform], regionConfig);
                                }
                                
                                if (value && value.trim()) {
                                    return value.trim();
                                }
                            }
                        } catch (e) {
                            console.warn(`Selector failed: ${selectorConfig.selector}`, e);
                        }
                    }
                    
                    // Try fallback if configured
                    if (fieldConfig.fallback) {
                        return applyFallback(fieldConfig.fallback, container, regionConfig);
                    }
                    
                    return null;
                }
                
                function extractValueFromElement(element, extraction) {
                    switch (extraction) {
                        case 'textContent':
                            return element.textContent;
                        case 'innerHTML':
                            return element.innerHTML;
                        case 'href':
                            return element.href;
                        case 'src':
                            return element.src;
                        default:
                            if (extraction.startsWith('data-')) {
                                return element.getAttribute(extraction);
                            }
                            return element.getAttribute(extraction) || element.textContent;
                    }
                }
                
                function applyTransformation(value, transformation, regionConfig) {
                    try {
                        // Create a safe evaluation context
                        const context = {
                            text: value,
                            baseUrl: regionConfig.baseUrl,
                            currency: regionConfig.currency
                        };
                        
                        // Simple transformation functions
                        if (transformation.function.includes('trim')) {
                            return value.trim().replace(/\\s+/g, ' ');
                        }
                        if (transformation.function.includes('normalizeUrl')) {
                            return value.startsWith('http') ? value : 
                                   (value.startsWith('/') ? regionConfig.baseUrl + value : regionConfig.baseUrl + '/' + value);
                        }
                        if (transformation.function.includes('extractDiscount')) {
                            const match = value.match(/(\\d+)\\s*(%|€)/);
                            return match ? `-${match[1]}${match[2]}` : value;
                        }
                        if (transformation.function.includes('extractRating')) {
                            const match = value.match(/(\\d+[,.]?\\d*)\\s*(?:sur|out of|\\/)/i);
                            return match ? match[1].replace(',', '.') : '';
                        }
                        
                        return value;
                    } catch (e) {
                        console.warn('Transformation failed:', e);
                        return value;
                    }
                }
                
                function applyFallback(fallback, container, regionConfig) {
                    if (fallback.generateFromASIN) {
                        const asin = container.getAttribute('data-asin');
                        if (asin && fallback.template) {
                            return fallback.template.replace('{baseUrl}', regionConfig.baseUrl).replace('{asin}', asin);
                        }
                    }
                    
                    if (fallback.extractFromUrl && fallback.pattern) {
                        const urlElement = container.querySelector('a[href]');
                        if (urlElement) {
                            const match = urlElement.href.match(new RegExp(fallback.pattern));
                            return match ? match[1] : null;
                        }
                    }
                    
                    return null;
                }
                
                function isValidProduct(product, config) {
                    // Basic validation - could be enhanced with config.validation rules
                    return product.title && (product.url || product.asin);
                }
            ";
        }

        /// <summary>
        /// Get rule set for page type
        /// </summary>
        private ExtractionRuleSet? GetRuleSetForPageType(ExtractionConfiguration config, string pageType)
        {
            return pageType.ToLower() switch
            {
                "productlist" => config.ExtractionRules.ProductList,
                "productpage" => config.ExtractionRules.ProductPage,
                _ => null
            };
        }

        /// <summary>
        /// Ensure configuration is loaded
        /// </summary>
        private async Task<ExtractionConfiguration> EnsureConfigurationLoadedAsync()
        {
            if (_currentConfig == null)
            {
                _currentConfig = await _configManager.LoadConfigurationAsync();
            }
            return _currentConfig;
        }

        /// <summary>
        /// Handle configuration changes
        /// </summary>
        private void OnConfigurationChanged(object? sender, ConfigurationChangedEventArgs e)
        {
            _currentConfig = e.NewConfiguration;
            Logger.LogInfo("Extraction configuration updated", "ConfigurableExtraction");
        }

        /// <summary>
        /// Get field value from product using reflection
        /// </summary>
        private string? GetFieldValue(ProductInfo product, string fieldName)
        {
            var property = typeof(ProductInfo).GetProperty(fieldName, 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.IgnoreCase);
            
            return property?.GetValue(product)?.ToString();
        }

        /// <summary>
        /// Validate individual field
        /// </summary>
        private void ValidateField(string fieldName, string? value, FieldValidation validation, ValidationResult result)
        {
            if (validation.Required && string.IsNullOrEmpty(value))
            {
                result.AddError($"Field '{fieldName}' is required but empty");
                return;
            }

            if (!string.IsNullOrEmpty(value))
            {
                if (validation.MinLength.HasValue && value.Length < validation.MinLength.Value)
                {
                    result.AddError($"Field '{fieldName}' is too short (min: {validation.MinLength.Value})");
                }

                if (validation.MaxLength.HasValue && value.Length > validation.MaxLength.Value)
                {
                    result.AddError($"Field '{fieldName}' is too long (max: {validation.MaxLength.Value})");
                }

                if (!string.IsNullOrEmpty(validation.Pattern) && !Regex.IsMatch(value, validation.Pattern))
                {
                    result.AddError($"Field '{fieldName}' doesn't match required pattern");
                }

                if (validation.MustContain.Any() && !validation.MustContain.Any(required => value.Contains(required)))
                {
                    result.AddError($"Field '{fieldName}' must contain one of: {string.Join(", ", validation.MustContain)}");
                }
            }
        }
    }

    /// <summary>
    /// Validation result for extracted data
    /// </summary>
    public class ValidationResult
    {
        public List<string> Errors { get; } = new();
        public List<string> Warnings { get; } = new();
        public bool IsValid => !Errors.Any();

        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
    }
}
