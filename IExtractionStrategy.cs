using PuppeteerSharp;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Interface for different extraction strategies
    /// </summary>
    public interface IExtractionStrategy
    {
        /// <summary>
        /// Extract products using this strategy
        /// </summary>
        Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config);

        /// <summary>
        /// Strategy name for logging and identification
        /// </summary>
        string StrategyName { get; }

        /// <summary>
        /// Whether this strategy supports fallback to another strategy
        /// </summary>
        bool SupportsFallback { get; }
    }

    /// <summary>
    /// Direct CSS selector-based extraction strategy
    /// </summary>
    public class DirectExtractionStrategy : IExtractionStrategy
    {
        public string StrategyName => "Direct CSS Scraping";
        public bool SupportsFallback => true;

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            var products = new List<ProductInfo>();
            
            Console.WriteLine($"🎯 Using {StrategyName} strategy...");

            try
            {
                // Navigate to Amazon deals page
                await page.GoToAsync("https://www.amazon.fr/deals?ref_=nav_cs_gb", 
                    new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });

                // Process multiple pages with infinite scroll
                for (int pageNum = 1; pageNum <= config.MaxPages; pageNum++)
                {
                    Console.WriteLine($"📄 Processing page {pageNum}/{config.MaxPages}...");

                    // Extract products from current view
                    var pageProducts = await ExtractProductsFromCurrentView(page);
                    products.AddRange(pageProducts);

                    Console.WriteLine($"   ✅ Found {pageProducts.Count} products on page {pageNum}");

                    // Handle infinite scroll by clicking "Show more" buttons
                    if (pageNum < config.MaxPages)
                    {
                        var hasMore = await HandleInfiniteScroll(page, config.MaxLoadMoreClicks);
                        if (!hasMore)
                        {
                            Console.WriteLine("   ⚠️ No more content available, stopping pagination");
                            break;
                        }
                    }

                    // Random delay between pages
                    await Task.Delay(Random.Shared.Next(2000, 5000));
                }

                // Remove duplicates based on URL
                products = products
                    .GroupBy(p => p.ProductUrl)
                    .Select(g => g.First())
                    .Where(p => p.IsValid())
                    .ToList();

                Console.WriteLine($"✅ Direct extraction completed: {products.Count} unique products");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Direct extraction failed: {ex.Message}");
                return products; // Return partial results
            }
        }

        private async Task<List<ProductInfo>> ExtractProductsFromCurrentView(IPage page)
        {
            return await page.EvaluateFunctionAsync<List<ProductInfo>>(@"() => {
                const products = [];
                
                // Updated selectors based on current Amazon structure
                const productCards = document.querySelectorAll('div[data-asin]:not([data-asin=""""])');
                
                productCards.forEach(card => {
                    try {
                        const asin = card.getAttribute('data-asin');
                        if (!asin) return;

                        // Extract title
                        const titleElement = card.querySelector('h3 a span, .s-size-mini span, [data-cy=""title-recipe-title""] span');
                        const title = titleElement?.textContent?.trim() || '';

                        // Extract URL
                        const linkElement = card.querySelector('h3 a, a[data-testid=""product-card-link""]');
                        let url = linkElement?.href || '';
                        if (url && !url.startsWith('http')) {
                            url = 'https://www.amazon.fr' + url;
                        }

                        // Extract price
                        const priceElement = card.querySelector('.a-price .a-offscreen, .a-price-whole');
                        const price = priceElement?.textContent?.trim() || '';

                        // Extract discount
                        const discountElement = card.querySelector('[class*=""badge""] span, .a-badge-text');
                        const discount = discountElement?.textContent?.trim() || '';

                        // Extract image
                        const imageElement = card.querySelector('img');
                        const imageUrl = imageElement?.src || imageElement?.getAttribute('data-src') || '';

                        if (title && url) {
                            products.push({
                                title: title,
                                productUrl: url,
                                imageUrl: imageUrl,
                                price: price,
                                originalPrice: '',
                                discount: discount,
                                rating: '',
                                reviewCount: '',
                                category: '',
                                affiliateLink: '',
                                commissionRate: '',
                                facebookPost: '',
                                isDeal: true,
                                extractedAt: new Date().toISOString()
                            });
                        }
                    } catch (e) {
                        console.warn('Error extracting product:', e);
                    }
                });

                return products;
            }");
        }

        private async Task<bool> HandleInfiniteScroll(IPage page, int maxClicks)
        {
            var clickCount = 0;
            
            while (clickCount < maxClicks)
            {
                try
                {
                    // Look for "Show more" or "Load more" buttons
                    var showMoreButton = await page.QuerySelectorAsync(
                        "button[aria-label*='Afficher plus'], " +
                        "button:has-text('Afficher plus'), " +
                        "button:has-text('Voir plus'), " +
                        "[data-testid='show-more-button']"
                    );

                    if (showMoreButton == null)
                    {
                        // Try scrolling to bottom to trigger lazy loading
                        await page.EvaluateExpressionAsync("window.scrollTo(0, document.body.scrollHeight)");
                        await Task.Delay(2000);
                        
                        // Check again for new button
                        showMoreButton = await page.QuerySelectorAsync(
                            "button[aria-label*='Afficher plus'], " +
                            "button:has-text('Afficher plus')"
                        );
                    }

                    if (showMoreButton == null)
                    {
                        Console.WriteLine($"   ⚠️ No more 'Show more' buttons found after {clickCount} clicks");
                        return false;
                    }

                    // Click the button
                    await showMoreButton.ClickAsync();
                    clickCount++;
                    
                    Console.WriteLine($"   🔄 Clicked 'Show more' button ({clickCount}/{maxClicks})");
                    
                    // Wait for content to load
                    await Task.Delay(Random.Shared.Next(2000, 4000));
                    
                    // Wait for new content to appear
                    await page.WaitForTimeoutAsync(1000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ Error during infinite scroll: {ex.Message}");
                    return false;
                }
            }

            return true; // Reached max clicks
        }
    }

    /// <summary>
    /// AI-based extraction strategy using Google Gemini
    /// </summary>
    public class AIExtractionStrategy : IExtractionStrategy
    {
        private readonly FacebookPostGenerator _postGenerator;

        public string StrategyName => "AI-Based Extraction";
        public bool SupportsFallback => false;

        public AIExtractionStrategy(string geminiApiKey)
        {
            _postGenerator = new FacebookPostGenerator(geminiApiKey);
        }

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            var products = new List<ProductInfo>();

            Console.WriteLine($"🤖 Using {StrategyName} strategy...");

            try
            {
                // Navigate to Amazon deals page
                await page.GoToAsync("https://www.amazon.fr/deals?ref_=nav_cs_gb",
                    new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });

                // Process pages with AI analysis
                for (int pageNum = 1; pageNum <= config.MaxPages; pageNum++)
                {
                    Console.WriteLine($"📄 AI analyzing page {pageNum}/{config.MaxPages}...");

                    // Scroll and load more content
                    await ScrollAndLoadMore(page, config.MaxLoadMoreClicks);

                    // Extract HTML content for AI analysis
                    var htmlContent = await page.GetContentAsync();

                    // Limit HTML size for AI processing (Gemini has token limits)
                    if (htmlContent.Length > 500000) // ~500KB limit
                    {
                        htmlContent = htmlContent[..500000];
                        Console.WriteLine("   📏 HTML content truncated for AI processing");
                    }

                    // Use AI to extract products
                    var pageProducts = await ExtractProductsWithAI(htmlContent, page.Url);
                    products.AddRange(pageProducts);

                    Console.WriteLine($"   ✅ AI found {pageProducts.Count} products on page {pageNum}");

                    // Random delay between pages
                    await Task.Delay(Random.Shared.Next(3000, 8000));
                }

                // Remove duplicates and validate
                products = products
                    .GroupBy(p => p.ProductUrl)
                    .Select(g => g.First())
                    .Where(p => p.IsValid())
                    .ToList();

                Console.WriteLine($"✅ AI extraction completed: {products.Count} unique products");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AI extraction failed: {ex.Message}");
                return products; // Return partial results
            }
        }

        private async Task ScrollAndLoadMore(IPage page, int maxClicks)
        {
            // Scroll to bottom to trigger lazy loading
            await page.EvaluateExpressionAsync(@"
                window.scrollTo(0, document.body.scrollHeight);
            ");
            await Task.Delay(2000);

            // Click "Show more" buttons
            for (int i = 0; i < maxClicks; i++)
            {
                try
                {
                    var showMoreButton = await page.QuerySelectorAsync(
                        "button[aria-label*='Afficher plus'], button:has-text('Afficher plus')"
                    );

                    if (showMoreButton == null) break;

                    await showMoreButton.ClickAsync();
                    await Task.Delay(Random.Shared.Next(2000, 4000));

                    Console.WriteLine($"   🔄 AI strategy: Clicked 'Show more' ({i + 1}/{maxClicks})");
                }
                catch
                {
                    break; // Stop if clicking fails
                }
            }
        }

        private async Task<List<ProductInfo>> ExtractProductsWithAI(string htmlContent, string pageUrl)
        {
            try
            {
                var prompt = CreateExtractionPrompt(htmlContent, pageUrl);
                var response = await _postGenerator.CallGeminiAPI(prompt);

                return ParseAIResponse(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ AI extraction error: {ex.Message}");
                return new List<ProductInfo>();
            }
        }

        private string CreateExtractionPrompt(string htmlContent, string pageUrl)
        {
            return $"""
                Analyze this Amazon deals page HTML and extract ALL product information in JSON format.

                HTML Content (truncated):
                {htmlContent}

                Page URL: {pageUrl}

                Extract products with these fields:
                - title: Product title
                - productUrl: Full Amazon product URL (must start with https://www.amazon.fr)
                - discount: Discount percentage or amount
                - price: Current price
                - imageUrl: Product image URL

                Return ONLY a JSON array of products. Example:
                [
                  {{
                    "title": "Product Name",
                    "productUrl": "https://www.amazon.fr/dp/ASIN",
                    "discount": "-25%",
                    "price": "29,99 €",
                    "imageUrl": "https://..."
                  }}
                ]

                Focus on finding actual product deals, ignore navigation elements, ads, and non-product content.
                """;
        }

        private List<ProductInfo> ParseAIResponse(string response)
        {
            try
            {
                // Clean the response to extract JSON
                var jsonStart = response.IndexOf('[');
                var jsonEnd = response.LastIndexOf(']');

                if (jsonStart == -1 || jsonEnd == -1) return new List<ProductInfo>();

                var jsonContent = response.Substring(jsonStart, jsonEnd - jsonStart + 1);

                var aiProducts = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(jsonContent);
                var products = new List<ProductInfo>();

                foreach (var aiProduct in aiProducts ?? new())
                {
                    var product = new ProductInfo
                    {
                        Title = GetStringValue(aiProduct, "title"),
                        ProductUrl = NormalizeUrl(GetStringValue(aiProduct, "productUrl")),
                        Discount = GetStringValue(aiProduct, "discount"),
                        Price = GetStringValue(aiProduct, "price"),
                        ImageUrl = GetStringValue(aiProduct, "imageUrl"),
                        IsDeal = true
                    };

                    if (product.IsValid())
                    {
                        products.Add(product);
                    }
                }

                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error parsing AI response: {ex.Message}");
                return new List<ProductInfo>();
            }
        }

        private static string GetStringValue(Dictionary<string, object> dict, string key)
        {
            return dict.TryGetValue(key, out var value) ? value?.ToString() ?? "" : "";
        }

        private static string NormalizeUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            if (url.StartsWith("/"))
                return "https://www.amazon.fr" + url;

            if (!url.StartsWith("http"))
                return "https://www.amazon.fr/" + url.TrimStart('/');

            return url;
        }
    }

    /// <summary>
    /// Hybrid strategy that tries direct extraction first, then falls back to AI
    /// </summary>
    public class HybridExtractionStrategy : IExtractionStrategy
    {
        private readonly DirectExtractionStrategy _directStrategy;
        private readonly AIExtractionStrategy _aiStrategy;

        public string StrategyName => "Hybrid (Direct + AI Fallback)";
        public bool SupportsFallback => false; // Already handles fallback internally

        public HybridExtractionStrategy(string geminiApiKey)
        {
            _directStrategy = new DirectExtractionStrategy();
            _aiStrategy = new AIExtractionStrategy(geminiApiKey);
        }

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            Console.WriteLine($"🔄 Using {StrategyName} strategy...");

            // Try direct extraction first
            var products = await _directStrategy.ExtractProductsAsync(page, config);

            // If direct extraction found few or no products, try AI fallback
            if (products.Count < 5 && config.EnableAiFallback)
            {
                Console.WriteLine($"⚠️ Direct extraction found only {products.Count} products, trying AI fallback...");

                var aiProducts = await _aiStrategy.ExtractProductsAsync(page, config);

                // Merge results, preferring direct extraction for duplicates
                var combinedProducts = products.ToList();
                var existingUrls = products.Select(p => p.ProductUrl).ToHashSet();

                foreach (var aiProduct in aiProducts)
                {
                    if (!existingUrls.Contains(aiProduct.ProductUrl))
                    {
                        combinedProducts.Add(aiProduct);
                    }
                }

                Console.WriteLine($"✅ Hybrid strategy: {products.Count} direct + {aiProducts.Count} AI = {combinedProducts.Count} total");
                return combinedProducts;
            }

            return products;
        }
    }
}
