using PuppeteerSharp;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Interface for different extraction strategies
    /// </summary>
    public interface IExtractionStrategy
    {
        /// <summary>
        /// Extract products using this strategy
        /// </summary>
        Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config);

        /// <summary>
        /// Strategy name for logging and identification
        /// </summary>
        string StrategyName { get; }

        /// <summary>
        /// Whether this strategy supports fallback to another strategy
        /// </summary>
        bool SupportsFallback { get; }
    }

    /// <summary>
    /// Direct CSS selector-based extraction strategy
    /// </summary>
    public class DirectExtractionStrategy : IExtractionStrategy
    {
        public string StrategyName => "Direct CSS Scraping";
        public bool SupportsFallback => true;

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            var products = new List<ProductInfo>();
            
            Console.WriteLine($"🎯 Using {StrategyName} strategy...");

            try
            {
                // Navigate to Amazon deals page
                await page.GoToAsync("https://www.amazon.fr/deals?ref_=nav_cs_gb", 
                    new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });

                // Process multiple pages with infinite scroll
                for (int pageNum = 1; pageNum <= config.MaxPages; pageNum++)
                {
                    Console.WriteLine($"📄 Processing page {pageNum}/{config.MaxPages}...");

                    // Extract products from current view
                    var pageProducts = await ExtractProductsFromCurrentView(page);

                    // Post-process products to set ASIN and other metadata
                    foreach (var product in pageProducts)
                    {
                        await PostProcessProduct(product, page);
                    }

                    products.AddRange(pageProducts);

                    Console.WriteLine($"   ✅ Found {pageProducts.Count} products on page {pageNum}");

                    // Handle infinite scroll by clicking "Show more" buttons
                    if (pageNum < config.MaxPages)
                    {
                        var hasMore = await HandleInfiniteScroll(page, config.MaxLoadMoreClicks);
                        if (!hasMore)
                        {
                            Console.WriteLine("   ⚠️ No more content available, stopping pagination");
                            break;
                        }
                    }

                    // Random delay between pages
                    await Task.Delay(Random.Shared.Next(2000, 5000));
                }

                // Remove duplicates based on URL
                products = products
                    .GroupBy(p => p.ProductUrl)
                    .Select(g => g.First())
                    .Where(p => p.IsValid())
                    .ToList();

                Console.WriteLine($"✅ Direct extraction completed: {products.Count} unique products");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Direct extraction failed: {ex.Message}");
                return products; // Return partial results
            }
        }

        private async Task<List<ProductInfo>> ExtractProductsFromCurrentView(IPage page)
        {
            return await page.EvaluateFunctionAsync<List<ProductInfo>>(@"() => {
                const products = [];

                // Enhanced selectors for dynamic class names and data attributes
                const productCardSelectors = [
                    // Primary selectors with data attributes
                    'div[data-testid=""product-card""][data-asin]:not([data-asin=""""])',
                    'div[data-testid=""product-card""]', // Without ASIN requirement as fallback

                    // Dynamic class-based selectors
                    'div[class*=""ProductCard-module__card""][data-asin]:not([data-asin=""""])',
                    'div[class*=""ProductCard-module__card""]',

                    // Generic selectors with ASIN
                    'div[data-asin]:not([data-asin=""""])',
                    '[data-asin]:not([data-asin=""""])',

                    // Fallback selectors for different Amazon layouts
                    'div[class*=""s-result-item""][data-asin]:not([data-asin=""""])',
                    'div[class*=""deal""][data-asin]:not([data-asin=""""])',
                    'div[class*=""product""][data-asin]:not([data-asin=""""])'
                ];

                let productCards = [];
                let usedSelector = '';

                for (const selector of productCardSelectors) {
                    try {
                        productCards = document.querySelectorAll(selector);
                        if (productCards.length > 0) {
                            usedSelector = selector;
                            console.log(`✅ Found ${productCards.length} products using selector: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        console.warn(`⚠️ Selector failed: ${selector}`, e);
                    }
                }

                if (productCards.length === 0) {
                    console.warn('❌ No product cards found with any selector');
                    return [];
                }

                productCards.forEach((card, index) => {
                    try {
                        // Extract ASIN directly from data attribute
                        const asin = card.getAttribute('data-asin');
                        const dealId = card.getAttribute('data-deal-id') || '';

                        if (!asin) {
                            console.warn(`Product card ${index} missing ASIN`);
                            return;
                        }

                        // Enhanced title extraction with multiple fallbacks and dynamic class handling
                        const titleSelectors = [
                            // Specific Amazon title selectors
                            '.a-truncate-full', // Full title in truncate container
                            '.a-truncate .a-truncate-full',

                            // Dynamic class-based selectors
                            '[class*=""truncate""] [class*=""full""]',
                            '[class*=""title""] span',

                            // Standard selectors
                            'h3 a span',
                            'h2 a span',
                            'h1 a span',

                            // Data attribute selectors
                            '[data-cy=""title-recipe-title""] span',
                            '[data-testid*=""title""] span',

                            // Size-based selectors
                            '.s-size-mini span',
                            '.s-size-small span',

                            // Link-based selectors
                            '.a-link-normal span',
                            'a[href*=""/dp/""] span',

                            // Fallback selectors
                            'span[class*=""title""]',
                            'div[class*=""title""] span'
                        ];

                        let title = '';
                        for (const selector of titleSelectors) {
                            try {
                                const titleElement = card.querySelector(selector);
                                if (titleElement?.textContent?.trim()) {
                                    title = titleElement.textContent.trim();
                                    // Clean up title (remove extra whitespace, newlines)
                                    title = title.replace(/\s+/g, ' ').trim();
                                    if (title.length > 10) { // Ensure it's a meaningful title
                                        break;
                                    }
                                }
                            } catch (e) {
                                console.warn(`Title selector failed: ${selector}`, e);
                            }
                        }

                        // Enhanced URL extraction
                        const linkSelectors = [
                            'a[data-testid=""product-card-link""]',
                            'h3 a',
                            'h2 a',
                            '.a-link-normal'
                        ];

                        let url = '';
                        for (const selector of linkSelectors) {
                            const linkElement = card.querySelector(selector);
                            if (linkElement?.href) {
                                url = linkElement.href;
                                break;
                            }
                        }

                        // Normalize URL
                        if (url && !url.startsWith('http')) {
                            url = 'https://www.amazon.fr' + url;
                        }

                        // Enhanced price extraction
                        const priceSelectors = [
                            '.a-price .a-offscreen',
                            '.a-price-whole',
                            '[class*=""price""] .a-offscreen',
                            '.a-price-range .a-offscreen'
                        ];

                        let price = '';
                        for (const selector of priceSelectors) {
                            const priceElement = card.querySelector(selector);
                            if (priceElement?.textContent?.trim()) {
                                price = priceElement.textContent.trim();
                                break;
                            }
                        }

                        // Enhanced discount extraction with badge handling and dynamic classes
                        const discountSelectors = [
                            // Specific Amazon badge selectors
                            'div[class*=""style_badgeContainer""] span',
                            'div[class*=""style_badgeContainer""]',

                            // Dynamic badge class selectors
                            '[class*=""badge""][class*=""Container""] span',
                            '[class*=""badge""][class*=""container""] span',
                            '[class*=""Badge""][class*=""Container""] span',

                            // Standard badge selectors
                            '[class*=""badge""] span',
                            '.a-badge-text',
                            '.a-badge span',

                            // Discount-specific selectors
                            '[class*=""discount""] span',
                            '[class*=""Discount""] span',
                            '[class*=""percent""] span',
                            '[class*=""Percent""] span',

                            // Deal-specific selectors
                            '[class*=""Deal""] span',
                            '[class*=""deal""] span',
                            '[class*=""offer""] span',
                            '[class*=""Offer""] span',

                            // Price-related selectors
                            '[class*=""save""] span',
                            '[class*=""Save""] span',
                            '[class*=""reduction""] span'
                        ];

                        let discount = '';
                        let dealType = '';

                        for (const selector of discountSelectors) {
                            try {
                                const discountElements = card.querySelectorAll(selector);
                                discountElements.forEach(element => {
                                    const text = element.textContent?.trim() || '';
                                    if (text) {
                                        // Check for percentage discounts
                                        if (text.match(/\d+\s*%/) && !discount) {
                                            discount = text;
                                        }
                                        // Check for euro amount discounts
                                        else if (text.match(/\d+(?:[,.]?\d+)?\s*€/) && !discount) {
                                            discount = text;
                                        }
                                        // Check for deal type descriptions
                                        else if ((text.includes('Offre') || text.includes('Deal') ||
                                                text.includes('Limited') || text.includes('durée') ||
                                                text.includes('Flash') || text.includes('Lightning')) && !dealType) {
                                            dealType = text;
                                        }
                                    }
                                });
                                if (discount && dealType) break;
                            } catch (e) {
                                console.warn(`Discount selector failed: ${selector}`, e);
                            }
                        }

                        // Enhanced image extraction
                        const imageSelectors = [
                            'img[src*=""images-amazon""]',
                            'img[data-src*=""images-amazon""]',
                            'img'
                        ];

                        let imageUrl = '';
                        for (const selector of imageSelectors) {
                            const imageElement = card.querySelector(selector);
                            if (imageElement) {
                                imageUrl = imageElement.src ||
                                          imageElement.getAttribute('data-src') ||
                                          imageElement.getAttribute('srcset')?.split(' ')[0] || '';
                                if (imageUrl) break;
                            }
                        }

                        // Extract rating and review count
                        let rating = '';
                        let reviewCount = '';

                        const ratingElement = card.querySelector('[class*=""rating""] span, .a-icon-alt');
                        if (ratingElement) {
                            const ratingText = ratingElement.textContent || ratingElement.getAttribute('aria-label') || '';
                            const ratingMatch = ratingText.match(/(\d+[,.]?\d*)\s*(?:sur|out of|\/)\s*5/i);
                            if (ratingMatch) {
                                rating = ratingMatch[1].replace(',', '.');
                            }
                        }

                        const reviewElement = card.querySelector('[class*=""review""] span, a[href*=""reviews""]');
                        if (reviewElement) {
                            const reviewText = reviewElement.textContent || '';
                            const reviewMatch = reviewText.match(/(\d+(?:[,.]?\d+)*)/);
                            if (reviewMatch) {
                                reviewCount = reviewMatch[1];
                            }
                        }

                        // Extract category if available
                        let category = '';
                        const categoryElement = card.querySelector('[class*=""category""] span, [class*=""department""] span');
                        if (categoryElement) {
                            category = categoryElement.textContent?.trim() || '';
                        }

                        // Validate essential data
                        if (title && (url || asin)) {
                            // Generate URL from ASIN if URL is missing
                            if (!url && asin) {
                                url = `https://www.amazon.fr/dp/${asin}`;
                            }

                            const product = {
                                title: title,
                                productUrl: url,
                                imageUrl: imageUrl,
                                price: price,
                                originalPrice: '', // Will be extracted from individual product pages
                                discount: discount,
                                rating: rating,
                                reviewCount: reviewCount,
                                category: category,
                                affiliateLink: '', // Will be generated later
                                commissionRate: '', // Will be extracted from individual product pages
                                facebookPost: '',
                                isDeal: true,
                                extractedAt: new Date().toISOString(),
                                dealId: dealId,
                                dealType: dealType,
                                // Store ASIN for direct access (will be set via SetASIN method)
                                _extractedASIN: asin
                            };

                            products.push(product);
                            console.log(`Extracted product ${index + 1}: ${title.substring(0, 50)}...`);
                        } else {
                            console.warn(`Product card ${index + 1} missing essential data - Title: ${!!title}, URL: ${!!url}, ASIN: ${!!asin}`);
                        }
                    } catch (e) {
                        console.error(`Error extracting product ${index + 1}:`, e);
                    }
                });

                console.log(`Total products extracted: ${products.length}`);
                return products;
            }");
        }

        private async Task<bool> HandleInfiniteScroll(IPage page, int maxClicks)
        {
            var clickCount = 0;
            
            while (clickCount < maxClicks)
            {
                try
                {
                    // Look for "Show more" or "Load more" buttons
                    var showMoreButton = await page.QuerySelectorAsync(
                        "button[aria-label*='Afficher plus'], " +
                        "button:has-text('Afficher plus'), " +
                        "button:has-text('Voir plus'), " +
                        "[data-testid='show-more-button']"
                    );

                    if (showMoreButton == null)
                    {
                        // Try scrolling to bottom to trigger lazy loading
                        await page.EvaluateExpressionAsync("window.scrollTo(0, document.body.scrollHeight)");
                        await Task.Delay(2000);
                        
                        // Check again for new button
                        showMoreButton = await page.QuerySelectorAsync(
                            "button[aria-label*='Afficher plus'], " +
                            "button:has-text('Afficher plus')"
                        );
                    }

                    if (showMoreButton == null)
                    {
                        Console.WriteLine($"   ⚠️ No more 'Show more' buttons found after {clickCount} clicks");
                        return false;
                    }

                    // Click the button
                    await showMoreButton.ClickAsync();
                    clickCount++;
                    
                    Console.WriteLine($"   🔄 Clicked 'Show more' button ({clickCount}/{maxClicks})");
                    
                    // Wait for content to load
                    await Task.Delay(Random.Shared.Next(2000, 4000));
                    
                    // Wait for new content to appear
                    await page.WaitForTimeoutAsync(1000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ Error during infinite scroll: {ex.Message}");
                    return false;
                }
            }

            return true; // Reached max clicks
        }

        /// <summary>
        /// Post-process extracted product to set ASIN and validate data
        /// </summary>
        private async Task PostProcessProduct(ProductInfo product, IPage page)
        {
            try
            {
                // Extract and set ASIN from the JavaScript extraction if available
                var extractedASIN = await page.EvaluateFunctionAsync<string>($@"() => {{
                    const cards = document.querySelectorAll('div[data-asin]');
                    for (const card of cards) {{
                        const titleElement = card.querySelector('.a-truncate-full, h3 a span, h2 a span');
                        if (titleElement && titleElement.textContent.trim() === '{product.Title.Replace("'", "\\'")}') {{
                            return card.getAttribute('data-asin') || '';
                        }}
                    }}
                    return '';
                }}");

                if (!string.IsNullOrEmpty(extractedASIN))
                {
                    product.SetASIN(extractedASIN);
                }

                // Validate and normalize product URL
                if (string.IsNullOrEmpty(product.ProductUrl) && !string.IsNullOrEmpty(product.ASIN))
                {
                    product.ProductUrl = $"https://www.amazon.fr/dp/{product.ASIN}";
                }

                // Normalize discount format
                if (!string.IsNullOrEmpty(product.Discount))
                {
                    product.Discount = NormalizeDiscount(product.Discount);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ Warning during post-processing: {ex.Message}");
            }
        }

        /// <summary>
        /// Normalize discount format for consistency
        /// </summary>
        private static string NormalizeDiscount(string discount)
        {
            if (string.IsNullOrEmpty(discount)) return "";

            // Extract percentage or amount
            var percentMatch = System.Text.RegularExpressions.Regex.Match(discount, @"(\d+)\s*%");
            if (percentMatch.Success)
            {
                return $"-{percentMatch.Groups[1].Value}%";
            }

            var amountMatch = System.Text.RegularExpressions.Regex.Match(discount, @"(\d+(?:[,.]?\d+)?)\s*€");
            if (amountMatch.Success)
            {
                return $"-{amountMatch.Groups[1].Value}€";
            }

            return discount; // Return as-is if no pattern matches
        }
    }

    /// <summary>
    /// AI-based extraction strategy using Google Gemini
    /// </summary>
    public class AIExtractionStrategy : IExtractionStrategy
    {
        private readonly FacebookPostGenerator _postGenerator;

        public string StrategyName => "AI-Based Extraction";
        public bool SupportsFallback => false;

        public AIExtractionStrategy(string geminiApiKey)
        {
            _postGenerator = new FacebookPostGenerator(geminiApiKey);
        }

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            var products = new List<ProductInfo>();

            Console.WriteLine($"🤖 Using {StrategyName} strategy...");

            try
            {
                // Navigate to Amazon deals page
                await page.GoToAsync("https://www.amazon.fr/deals?ref_=nav_cs_gb",
                    new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });

                // Process pages with AI analysis
                for (int pageNum = 1; pageNum <= config.MaxPages; pageNum++)
                {
                    Console.WriteLine($"📄 AI analyzing page {pageNum}/{config.MaxPages}...");

                    // Scroll and load more content
                    await ScrollAndLoadMore(page, config.MaxLoadMoreClicks);

                    // Extract HTML content for AI analysis
                    var htmlContent = await page.GetContentAsync();

                    // Limit HTML size for AI processing (Gemini has token limits)
                    if (htmlContent.Length > 500000) // ~500KB limit
                    {
                        htmlContent = htmlContent[..500000];
                        Console.WriteLine("   📏 HTML content truncated for AI processing");
                    }

                    // Use AI to extract products
                    var pageProducts = await ExtractProductsWithAI(htmlContent, page.Url);
                    products.AddRange(pageProducts);

                    Console.WriteLine($"   ✅ AI found {pageProducts.Count} products on page {pageNum}");

                    // Random delay between pages
                    await Task.Delay(Random.Shared.Next(3000, 8000));
                }

                // Remove duplicates and validate
                products = products
                    .GroupBy(p => p.ProductUrl)
                    .Select(g => g.First())
                    .Where(p => p.IsValid())
                    .ToList();

                Console.WriteLine($"✅ AI extraction completed: {products.Count} unique products");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AI extraction failed: {ex.Message}");
                return products; // Return partial results
            }
        }

        private async Task ScrollAndLoadMore(IPage page, int maxClicks)
        {
            // Scroll to bottom to trigger lazy loading
            await page.EvaluateExpressionAsync(@"
                window.scrollTo(0, document.body.scrollHeight);
            ");
            await Task.Delay(2000);

            // Click "Show more" buttons
            for (int i = 0; i < maxClicks; i++)
            {
                try
                {
                    var showMoreButton = await page.QuerySelectorAsync(
                        "button[aria-label*='Afficher plus'], button:has-text('Afficher plus')"
                    );

                    if (showMoreButton == null) break;

                    await showMoreButton.ClickAsync();
                    await Task.Delay(Random.Shared.Next(2000, 4000));

                    Console.WriteLine($"   🔄 AI strategy: Clicked 'Show more' ({i + 1}/{maxClicks})");
                }
                catch
                {
                    break; // Stop if clicking fails
                }
            }
        }

        private async Task<List<ProductInfo>> ExtractProductsWithAI(string htmlContent, string pageUrl)
        {
            try
            {
                var prompt = CreateExtractionPrompt(htmlContent, pageUrl);
                var response = await _postGenerator.CallGeminiAPI(prompt);

                return ParseAIResponse(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ AI extraction error: {ex.Message}");
                return new List<ProductInfo>();
            }
        }

        private string CreateExtractionPrompt(string htmlContent, string pageUrl)
        {
            return $"""
                Analyze this Amazon deals page HTML and extract ALL product information in JSON format.

                HTML Content (truncated):
                {htmlContent}

                Page URL: {pageUrl}

                Extract products with these fields:
                - title: Product title
                - productUrl: Full Amazon product URL (must start with https://www.amazon.fr)
                - discount: Discount percentage or amount
                - price: Current price
                - imageUrl: Product image URL

                Return ONLY a JSON array of products. Example:
                [
                  {{
                    "title": "Product Name",
                    "productUrl": "https://www.amazon.fr/dp/ASIN",
                    "discount": "-25%",
                    "price": "29,99 €",
                    "imageUrl": "https://..."
                  }}
                ]

                Focus on finding actual product deals, ignore navigation elements, ads, and non-product content.
                """;
        }

        private List<ProductInfo> ParseAIResponse(string response)
        {
            try
            {
                // Clean the response to extract JSON
                var jsonStart = response.IndexOf('[');
                var jsonEnd = response.LastIndexOf(']');

                if (jsonStart == -1 || jsonEnd == -1) return new List<ProductInfo>();

                var jsonContent = response.Substring(jsonStart, jsonEnd - jsonStart + 1);

                var aiProducts = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(jsonContent);
                var products = new List<ProductInfo>();

                foreach (var aiProduct in aiProducts ?? new())
                {
                    var product = new ProductInfo
                    {
                        Title = GetStringValue(aiProduct, "title"),
                        ProductUrl = NormalizeUrl(GetStringValue(aiProduct, "productUrl")),
                        Discount = GetStringValue(aiProduct, "discount"),
                        Price = GetStringValue(aiProduct, "price"),
                        ImageUrl = GetStringValue(aiProduct, "imageUrl"),
                        IsDeal = true
                    };

                    if (product.IsValid())
                    {
                        products.Add(product);
                    }
                }

                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error parsing AI response: {ex.Message}");
                return new List<ProductInfo>();
            }
        }

        private static string GetStringValue(Dictionary<string, object> dict, string key)
        {
            return dict.TryGetValue(key, out var value) ? value?.ToString() ?? "" : "";
        }

        private static string NormalizeUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            if (url.StartsWith("/"))
                return "https://www.amazon.fr" + url;

            if (!url.StartsWith("http"))
                return "https://www.amazon.fr/" + url.TrimStart('/');

            return url;
        }
    }

    /// <summary>
    /// Hybrid strategy that tries direct extraction first, then falls back to AI
    /// </summary>
    public class HybridExtractionStrategy : IExtractionStrategy
    {
        private readonly DirectExtractionStrategy _directStrategy;
        private readonly AIExtractionStrategy _aiStrategy;

        public string StrategyName => "Hybrid (Direct + AI Fallback)";
        public bool SupportsFallback => false; // Already handles fallback internally

        public HybridExtractionStrategy(string geminiApiKey)
        {
            _directStrategy = new DirectExtractionStrategy();
            _aiStrategy = new AIExtractionStrategy(geminiApiKey);
        }

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            Console.WriteLine($"🔄 Using {StrategyName} strategy...");

            // Try direct extraction first
            var products = await _directStrategy.ExtractProductsAsync(page, config);

            // If direct extraction found few or no products, try AI fallback
            if (products.Count < 5 && config.EnableAiFallback)
            {
                Console.WriteLine($"⚠️ Direct extraction found only {products.Count} products, trying AI fallback...");

                var aiProducts = await _aiStrategy.ExtractProductsAsync(page, config);

                // Merge results, preferring direct extraction for duplicates
                var combinedProducts = products.ToList();
                var existingUrls = products.Select(p => p.ProductUrl).ToHashSet();

                foreach (var aiProduct in aiProducts)
                {
                    if (!existingUrls.Contains(aiProduct.ProductUrl))
                    {
                        combinedProducts.Add(aiProduct);
                    }
                }

                Console.WriteLine($"✅ Hybrid strategy: {products.Count} direct + {aiProducts.Count} AI = {combinedProducts.Count} total");
                return combinedProducts;
            }

            return products;
        }
    }

    /// <summary>
    /// Configurable extraction strategy that uses external configuration files
    /// </summary>
    public class ConfigurableExtractionStrategy : IExtractionStrategy
    {
        private readonly ConfigurableExtractionEngine _extractionEngine;
        private readonly ExtractionConfigurationManager _configManager;

        public string StrategyName => "Configurable Extraction";
        public bool SupportsFallback => true;

        public ConfigurableExtractionStrategy(string configPath = "extraction-rules.json", string region = "amazon.fr")
        {
            _configManager = new ExtractionConfigurationManager(configPath);
            _extractionEngine = new ConfigurableExtractionEngine(_configManager, region);
        }

        public async Task<List<ProductInfo>> ExtractProductsAsync(IPage page, AmazonScrapingConfiguration config)
        {
            Console.WriteLine($"🔧 Using {StrategyName} strategy...");

            try
            {
                // Navigate to Amazon deals page
                await page.GoToAsync("https://www.amazon.fr/deals?ref_=nav_cs_gb",
                    new NavigationOptions { WaitUntil = new[] { WaitUntilNavigation.Networkidle0 } });

                var allProducts = new List<ProductInfo>();

                // Process multiple pages
                for (int pageNum = 1; pageNum <= config.MaxPages; pageNum++)
                {
                    Console.WriteLine($"📄 Processing page {pageNum}/{config.MaxPages} with configurable extraction...");

                    // Extract products using configuration
                    var pageProducts = await _extractionEngine.ExtractProductsAsync(page, "productList");

                    // Post-process products
                    foreach (var product in pageProducts)
                    {
                        // Set ASIN if extracted
                        if (!string.IsNullOrEmpty(product.ASIN))
                        {
                            product.SetASIN(product.ASIN);
                        }

                        // Validate extracted data
                        var validationResult = _extractionEngine.ValidateExtractedData(product, "productList");
                        if (!validationResult.IsValid)
                        {
                            Console.WriteLine($"   ⚠️ Validation warnings for {product.Title}: {string.Join(", ", validationResult.Errors)}");
                        }
                    }

                    allProducts.AddRange(pageProducts);
                    Console.WriteLine($"   ✅ Found {pageProducts.Count} products on page {pageNum}");

                    // Handle infinite scroll for next page
                    if (pageNum < config.MaxPages)
                    {
                        var hasMore = await HandleInfiniteScrollConfigurable(page, config.MaxLoadMoreClicks);
                        if (!hasMore)
                        {
                            Console.WriteLine("   ⚠️ No more content available, stopping pagination");
                            break;
                        }
                    }

                    // Random delay between pages
                    await Task.Delay(Random.Shared.Next(2000, 5000));
                }

                // Remove duplicates and validate
                var uniqueProducts = allProducts
                    .GroupBy(p => p.ProductUrl)
                    .Select(g => g.First())
                    .Where(p => p.IsValid())
                    .ToList();

                Console.WriteLine($"✅ Configurable extraction completed: {uniqueProducts.Count} unique products");
                return uniqueProducts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configurable extraction failed: {ex.Message}");
                return new List<ProductInfo>(); // Return empty list on failure
            }
        }

        /// <summary>
        /// Handle infinite scroll using configurable approach
        /// </summary>
        private async Task<bool> HandleInfiniteScrollConfigurable(IPage page, int maxClicks)
        {
            var clickCount = 0;

            while (clickCount < maxClicks)
            {
                try
                {
                    // Use configurable selectors for "Show more" buttons
                    var showMoreButton = await page.QuerySelectorAsync(
                        "button[aria-label*='Afficher plus'], " +
                        "button:has-text('Afficher plus'), " +
                        "button:has-text('Voir plus'), " +
                        "[data-testid='show-more-button']"
                    );

                    if (showMoreButton == null)
                    {
                        // Try scrolling to trigger lazy loading
                        await page.EvaluateExpressionAsync("window.scrollTo(0, document.body.scrollHeight)");
                        await Task.Delay(2000);

                        // Check again for new button
                        showMoreButton = await page.QuerySelectorAsync(
                            "button[aria-label*='Afficher plus'], " +
                            "button:has-text('Afficher plus')"
                        );
                    }

                    if (showMoreButton == null)
                    {
                        Console.WriteLine($"   ⚠️ No more 'Show more' buttons found after {clickCount} clicks");
                        return false;
                    }

                    // Click the button
                    await showMoreButton.ClickAsync();
                    clickCount++;

                    Console.WriteLine($"   🔄 Clicked 'Show more' button ({clickCount}/{maxClicks})");

                    // Wait for content to load
                    await Task.Delay(Random.Shared.Next(2000, 4000));

                    // Wait for new content to appear
                    await page.WaitForTimeoutAsync(1000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ Error during infinite scroll: {ex.Message}");
                    return false;
                }
            }

            return true; // Reached max clicks
        }

        /// <summary>
        /// Test selectors against current page
        /// </summary>
        public async Task<SelectorTestResult> TestSelectorsAsync(IPage page)
        {
            return await _extractionEngine.TestSelectorsAsync(page, "productList");
        }

        /// <summary>
        /// Get current configuration
        /// </summary>
        public ExtractionConfiguration? GetCurrentConfiguration()
        {
            return _configManager.GetCurrentConfiguration();
        }

        /// <summary>
        /// Reload configuration
        /// </summary>
        public async Task<ExtractionConfiguration> ReloadConfigurationAsync()
        {
            return await _configManager.ReloadConfigurationAsync();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _configManager?.Dispose();
        }
    }
}
