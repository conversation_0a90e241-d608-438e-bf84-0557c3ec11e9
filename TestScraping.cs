using Amazon2FacebookPoster;
using Amazon2FacebookPoster.Diagnostics;

namespace Amazon2FacebookPoster.Tests
{
    /// <summary>
    /// Programme de test spécifique pour diagnostiquer et corriger les problèmes de scraping
    /// </summary>
    public class TestScraping
    {
        public static async Task RunTestMenu(string[] args)
        {
            Console.WriteLine("🔧 TEST DE DIAGNOSTIC DU SCRAPING AMAZON");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine();

            if (args.Length > 0 && args[0] == "--diagnostic")
            {
                await ScrapingDiagnostic.DiagnoseScrapingIssues();
                return;
            }

            if (args.Length > 0 && args[0] == "--extractor")
            {
                await ScrapingDiagnostic.TestProductExtractor();
                return;
            }

            // Menu de test
            while (true)
            {
                Console.Clear();
                Console.WriteLine("🔧 MENU DE TEST DU SCRAPING");
                Console.WriteLine("=" + new string('=', 30));
                Console.WriteLine();
                Console.WriteLine("1. 🔍 Diagnostic complet de la page Amazon");
                Console.WriteLine("2. 🧪 Test de l'extracteur de produits");
                Console.WriteLine("3. 📄 Test de pagination");
                Console.WriteLine("4. 🌐 Test simple de navigation");
                Console.WriteLine("5. 📋 Générer rapport de diagnostic");
                Console.WriteLine("6. ❌ Quitter");
                Console.WriteLine();
                Console.Write("Choisissez une option (1-6): ");

                var choice = Console.ReadLine();

                try
                {
                    switch (choice)
                    {
                        case "1":
                            await ScrapingDiagnostic.DiagnoseScrapingIssues();
                            break;
                        case "2":
                            await ScrapingDiagnostic.TestProductExtractor();
                            break;
                        case "3":
                            await ScrapingDiagnostic.TestPagination();
                            break;
                        case "4":
                            await TestSimpleNavigation();
                            break;
                        case "5":
                            await ScrapingDiagnostic.GenerateFullDiagnosticReport();
                            break;
                        case "6":
                            Console.WriteLine("👋 Au revoir !");
                            return;
                        default:
                            Console.WriteLine("❌ Option invalide.");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur: {ex.Message}");
                }

                Console.WriteLine();
                Console.WriteLine("Appuyez sur une touche pour continuer...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// Test simple de navigation vers Amazon
        /// </summary>
        private static async Task TestSimpleNavigation()
        {
            Console.WriteLine("🌐 TEST SIMPLE DE NAVIGATION");
            Console.WriteLine("=" + new string('=', 35));
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();

            try
            {
                Console.WriteLine("1️⃣ Démarrage du navigateur...");
                await amazonLoader.StartBrowser(headless: false);
                Console.WriteLine("✅ Navigateur démarré");

                Console.WriteLine();
                Console.WriteLine("2️⃣ Navigation vers Amazon.fr...");
                await amazonLoader.LoadPage("https://www.amazon.fr", async (page) =>
                {
                    Console.WriteLine($"✅ Page chargée : {page.Url}");
                    Console.WriteLine($"📄 Titre : {await page.GetTitleAsync()}");
                    return "";
                });

                Console.WriteLine();
                Console.WriteLine("3️⃣ Navigation vers la page des offres...");
                await amazonLoader.LoadPage("https://www.amazon.fr/deals", async (page) =>
                {
                    Console.WriteLine($"✅ Page des offres chargée : {page.Url}");
                    Console.WriteLine($"📄 Titre : {await page.GetTitleAsync()}");
                    
                    // Compter les éléments basiques
                    var elementCount = await page.EvaluateFunctionAsync<int>("() => document.querySelectorAll('*').length");
                    Console.WriteLine($"📊 Total d'éléments HTML : {elementCount}");
                    
                    var linkCount = await page.EvaluateFunctionAsync<int>("() => document.querySelectorAll('a').length");
                    Console.WriteLine($"🔗 Total de liens : {linkCount}");
                    
                    var productLinkCount = await page.EvaluateFunctionAsync<int>("() => document.querySelectorAll('a[href*=\"/dp/\"]').length");
                    Console.WriteLine($"🛒 Liens vers des produits (/dp/) : {productLinkCount}");
                    
                    return "";
                });

                await amazonLoader.CloseBrowser();
                Console.WriteLine();
                Console.WriteLine("✅ Test de navigation terminé avec succès");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur : {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Test rapide pour vérifier si le problème est résolu
        /// </summary>
        public static async Task QuickTest()
        {
            Console.WriteLine("⚡ TEST RAPIDE DE SCRAPING");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine();

            var amazonLoader = new AmazonLoader<string>();
            var extractor = new AmazonDealsExtractor(amazonLoader);

            try
            {
                await amazonLoader.StartBrowser(headless: false);
                Console.WriteLine("✅ Navigateur démarré");

                var products = await extractor.ExtractAllDealsProducts(maxPages: 1);
                
                Console.WriteLine($"📊 RÉSULTAT : {products.Count} produits extraits");
                
                if (products.Count > 0)
                {
                    Console.WriteLine("🎉 SUCCÈS ! Le scraping fonctionne !");
                    Console.WriteLine();
                    Console.WriteLine("Premiers produits trouvés :");
                    for (int i = 0; i < Math.Min(products.Count, 3); i++)
                    {
                        var product = products[i];
                        Console.WriteLine($"  {i + 1}. {product.Title}");
                        Console.WriteLine($"     URL: {product.ProductUrl}");
                        Console.WriteLine($"     Prix: {product.Price}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("❌ ÉCHEC : Aucun produit extrait");
                    Console.WriteLine("Le problème persiste, utilisez les autres options de diagnostic.");
                }

                await amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur : {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }

        /// <summary>
        /// Test avec différentes URLs Amazon pour identifier les problèmes
        /// </summary>
        public static async Task TestDifferentUrls()
        {
            Console.WriteLine("🔗 TEST AVEC DIFFÉRENTES URLs AMAZON");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();

            var testUrls = new[]
            {
                "https://www.amazon.fr",
                "https://www.amazon.fr/deals",
                "https://www.amazon.fr/deals?ref_=nav_cs_gb",
                "https://www.amazon.fr/gp/goldbox",
                "https://www.amazon.fr/s?k=deals"
            };

            var amazonLoader = new AmazonLoader<string>();

            try
            {
                await amazonLoader.StartBrowser(headless: false);

                foreach (var url in testUrls)
                {
                    Console.WriteLine($"🧪 Test de : {url}");
                    
                    try
                    {
                        await amazonLoader.LoadPage(url, async (page) =>
                        {
                            var title = await page.GetTitleAsync();
                            var productLinks = await page.EvaluateFunctionAsync<int>("() => document.querySelectorAll('a[href*=\"/dp/\"]').length");
                            var dataElements = await page.EvaluateFunctionAsync<int>("() => document.querySelectorAll('[data-asin]').length");
                            
                            Console.WriteLine($"  ✅ Titre: {title}");
                            Console.WriteLine($"  🔗 Liens produits: {productLinks}");
                            Console.WriteLine($"  📊 Éléments data-asin: {dataElements}");
                            
                            return "";
                        });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  ❌ Erreur: {ex.Message}");
                    }
                    
                    Console.WriteLine();
                    await Task.Delay(2000); // Délai entre les tests
                }

                await amazonLoader.CloseBrowser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur générale : {ex.Message}");
                await amazonLoader.CloseBrowser();
            }
        }
    }
}
